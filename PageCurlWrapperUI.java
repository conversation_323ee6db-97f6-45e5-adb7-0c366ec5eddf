/**
 *
 */
package com.tct.exbook.pdf.info.wrapper;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnClickListener;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.MenuItem.OnMenuItemClickListener;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnLongClickListener;
import android.view.ViewGroup;
import android.view.ViewTreeObserver.OnGlobalLayoutListener;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.compose.ui.platform.ComposeView;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.tct.exbook.R;
import com.tct.exbook.android.utils.Apps;
import com.tct.exbook.android.utils.Dips;
import com.tct.exbook.android.utils.IntegerResponse;
import com.tct.exbook.android.utils.Keyboards;
import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.android.utils.TxtUtils;
import com.tct.exbook.android.utils.Vibro;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.model.AppBookmark;
import com.tct.exbook.model.AppSP;
import com.tct.exbook.model.AppState;
import com.tct.exbook.pdf.info.AppsConfig;
import com.tct.exbook.pdf.info.BookmarksData;
import com.tct.exbook.pdf.info.DictsHelper;
import com.tct.exbook.pdf.info.ExtUtils;
import com.tct.exbook.pdf.info.OutlineHelper;
import com.tct.exbook.pdf.info.TintUtil;
import com.tct.exbook.pdf.info.model.AnnotationType;
import com.tct.exbook.pdf.info.model.BookCSS;
import com.tct.exbook.pdf.info.model.OutlineLinkWrapper;
import com.tct.exbook.pdf.info.view.AnchorHelper;
import com.tct.exbook.pdf.info.view.BrightnessHelper;
import com.tct.exbook.pdf.info.view.CustomSeek;
import com.tct.exbook.pdf.info.view.DialogsPlaylist;
import com.tct.exbook.pdf.info.view.DragingDialogs;
import com.tct.exbook.pdf.info.view.DrawView;
import com.tct.exbook.pdf.info.view.HorizontallSeekTouchEventListener;
import com.tct.exbook.pdf.info.view.HypenPanelHelper;
import com.tct.exbook.pdf.info.view.MyPopupMenu;
import com.tct.exbook.pdf.info.widget.DraggbleTouchListener;
import com.tct.exbook.pdf.search.activity.msg.MessagePageXY;
import com.tct.exbook.pdf.search.activity.msg.MessegeBrightness;
import com.tct.exbook.sys.TempHolder;
import com.tct.exbook.tts.MessagePageNumber;
import com.tct.exbook.ui2.MainTabs2;

import org.ebookdroid.BookType;
import org.ebookdroid.common.settings.books.SharedBooks;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.core.models.DocumentModel;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import kotlin.Unit;

/**
 * <AUTHOR>
 */
public class PageCurlWrapperUI {

    final DocumentController dc;
    private DocumentModel dm;
    final Handler handler = new Handler(Looper.getMainLooper());
    final Handler handlerTimer = new Handler(Looper.getMainLooper());


    //    public View.OnClickListener onLockUnlock = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            AppSP.get().isLocked = !AppSP.get().isLocked;
//            updateLock();
//        }
//    };
//    public View.OnClickListener onNextType = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            LOG.d("DEBUG", "Click");
//            doChooseNextType(arg0);
//        }
//    };
    public View.OnClickListener onSun = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            arg0.setEnabled(false);
            dc.onNightMode();
        }
    };
    //    public View.OnClickListener toPage = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            dc.toPageDialog();
//        }
//    };
    public View.OnClickListener onClose = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            //ImageLoader.getInstance().clearAllTasks();
            //Glide.with(LibreraApp.context).
//            closeDialogs();
            closeAndRunList();
        }
    };
    public View.OnClickListener onMoveLeft = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            dc.onSrollLeft();
        }
    };
    public View.OnClickListener onMoveCenter = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            dc.alignDocument();
        }
    };
    public View.OnClickListener onMoveRight = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            dc.onSrollRight();
        }
    };
    public View.OnClickListener onNextPage = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            nextChose(false);
        }
    };
    public View.OnClickListener onPrevPage = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            prevChose(false);
        }
    };
    public View.OnClickListener onPlus = new View.OnClickListener() {

        public void onClick(final View arg0) {
            dc.onZoomInc();
        }
    };
    public View.OnClickListener onMinus = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            dc.onZoomDec();
        }
    };
    Activity a;
    String bookTitle;
    TextView toastBrightnessText, floatingBookmarkTextView, pagesCountIndicator, percent;

    // 底部按钮布局
    private LinearLayout footerCatalog, footerProgress, footerStyle, footerReflow;
    // 底部按钮图标
    private ImageView footerCatalogImage, footerProgressImage, footerStyleImage, footerReflowImage;
    // 底部按钮文本
    private TextView footerCatalogText, footerProgressText, footerStyleText, footerReflowText;
    // 内容组件
    private LinearLayout catalogLayout, progressLayout, styleLayout;
    private int currentLayout = 1;

    private View overlay2;
//    public View.OnClickListener onModeChangeClick = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View v) {
//            MyPopupMenu p = new MyPopupMenu(v.getContext(), v);
//
//            p.getMenu().add(R.string.one_page).setIcon(R.drawable.my_glyphicons_two_page_one).setOnMenuItemClickListener(new OnMenuItemClickListener() {
//
//                @Override
//                public boolean onMenuItemClick(MenuItem item) {
//                    closeDialogs();
//                    onModeChange.setImageResource(R.drawable.my_glyphicons_two_page_one);
//                    AppSP.get().isCut = !false;
//                    onCut.onClick(null);
//                    hideShowEditIcon();
//                    return false;
//                }
//            });
//            p.getMenu().add(R.string.half_page).setIcon(R.drawable.my_glyphicons_page_split).setOnMenuItemClickListener(new OnMenuItemClickListener() {
//
//                @Override
//                public boolean onMenuItemClick(MenuItem item) {
//                    closeDialogs();
//                    onModeChange.setImageResource(R.drawable.my_glyphicons_page_split);
//                    AppSP.get().isCut = !true;
//                    onCut.onClick(null);
//                    hideShowEditIcon();
//                    return false;
//                }
//            });
//            p.show();
//            Keyboards.hideNavigation(dc.getActivity());
//
//        }
//    };

    // footer menu
    ImageView onModeChange;
    View titleBar, overlay;
    ImageView onCloseBook;

    private ImageView bookmark2, share;

    private View menuLayout, bottomBar;

    // progress
    private ImageView progressPrev;
    private ImageView progressNext;
    private SeekBar progressSeekBar;
    private TextView catalogCurProgress, catalogTitle;

    // footer_style
    // 字体大小相关
    private ImageView fontSizeDecrease;
    private AppCompatSeekBar fontSizeSeekBar;
    private ImageView fontSizeIncrease;
    private TextView fontSizeValue;
    // 文本增强相关
    private ImageView textEnhancementDecrease;
    private AppCompatSeekBar textEnhancementSeekBar;
    private ImageView textEnhancementIncrease;
    private TextView textEnhancementValue;
    // 裁剪相关
    private LinearLayout fitScreen;
    private LinearLayout fitWidth;
    private LinearLayout styleCrop;
    private LinearLayout styleReset;
    // 裁剪相关
    private ImageView fitScreenImage;
    private TextView fitScreenText;
    private ImageView fitWidthImage;
    private TextView fitWidthText;
    private ImageView styleCropImage;
    private TextView styleCropText;
    private ImageView styleResetImage;
    private TextView styleResetText;
    // 翻页样式相关
    private LinearLayout pageTurnFlip;
    private LinearLayout pageTurnSlide;
    private LinearLayout pageTurnScroll;
    private ImageView pageTurnFlipImage;
    private TextView pageTurnFlipText;
    private ImageView pageTurnSlideImage;
    private TextView pageTurnSlideText;
    private ImageView pageTurnScrollImage;
    private TextView pageTurnScrollText;
    // 页面缩放相关
    private LinearLayout pageZoomIn;
    private LinearLayout pageZoomOut;
    private ImageView pageZoomInImage;
    private TextView pageZoomInText;
    private ImageView pageZoomOutImage;
    private TextView pageZoomOutText;

    // header
    private TextView chapterTitle;
    private ImageView bookmarkBtn;

    // 侧边栏
    private DrawerLayout drawerLayout;
    private LinearLayout sidebar;
    // 侧边栏界面
    private ComposeView chapterMenuView;
    private ChapterMenu chapterMenu;

    public OnLongClickListener onCloseLongClick = new OnLongClickListener() {

        @Override
        public boolean onLongClick(final View v) {
            Vibro.vibrate();
//            CloseAppDialog.showOnLongClickDialog(a, v, getController());
//            hideAds();
            return true;
        }
    };
    View line1, line2, lineFirst, lineClose, parentParent, documentTitleBar;
    //    TTSControlsView ttsActive;
    SeekBar seekBar;
    FrameLayout anchor;
    //    public View.OnClickListener onShowContext = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            DragingDialogs.dialogShowContent(anchor, dc);
//        }
//    };
    public View.OnClickListener onBCclick = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            DragingDialogs.dialogContrastAndBrigtness(anchor, dc, new Runnable() {

                @Override
                public void run() {
//                    onBC.underline(AppState.get().isEnableBC);
                    dc.updateRendering();
                }
            }, null);
        }
    };
    ImageView anchorX, anchorY;
    DrawView drawView;
    //    public View.OnClickListener onHideShowToolBar = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            doHideShowToolBar();
//        }
//    };
//    UnderlineImageView crop, cut,;
    LinearLayout pageshelper;
    //    public View.OnClickListener onShowSearch = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            showSearchDialog();
//        }
//
//    };
    String quickBookmark;
    Runnable clearFlags = new Runnable() {

        @Override
        public void run() {
            try {
                dc.getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                LOG.d("FLAG clearFlags", "FLAG_KEEP_SCREEN_ON", "clear");
            } catch (Exception e) {
                LOG.e(e);
            }
        }
    };
    //    View.OnClickListener onRecent = new View.OnClickListener() {
//
//        @Override
//        public void onClick(View v) {
//            DragingDialogs.dialogRecentBooks(anchor, dc);
//        }
//    };
//    View.OnClickListener onThumbnail = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View v) {
//            DragingDialogs.dialogGoToPage(anchor, dc);
//        }
//    };
    SeekBar.OnSeekBarChangeListener onSeek = new SeekBar.OnSeekBarChangeListener() {

        @Override
        public void onStopTrackingTouch(final SeekBar seekBar) {
        }

        @Override
        public void onStartTrackingTouch(final SeekBar seekBar) {
        }

        @Override
        public void onProgressChanged(final SeekBar seekBar, final int progress, final boolean fromUser) {

            dc.onGoToPage(progress + 1);

            Apps.accessibilityText(a, a.getString(R.string.m_current_page) + " " + dc.getCurentPageFirst1());
            //updateUI();
        }
    };


    public PageCurlWrapperUI(final DocumentController controller) {
        AppState.get().annotationDrawColor = "";
        AppState.get().editWith = AppState.EDIT_NONE;
        this.dc = controller;
        EventBus.getDefault().register(this);

    }

    public void setDocumentModel(DocumentModel dm) {
        this.dm = dm;
    }

    public static boolean isCJK(int ch) {
        Character.UnicodeBlock block = Character.UnicodeBlock.of(ch);
        if (Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS.equals(block) || Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS.equals(block) || Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A.equals(block)) {
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPageNumber(MessagePageNumber event) {
        try {
            if (dc != null) {
                dc.onGoToPage(event.getPage() + 1);
//                ttsActive.setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            LOG.e(e);
        }
    }

    public void onSingleTap() {
        doShowHideWrapperControlls();
    }

    public void onLongPress(MotionEvent ev, RectF selectedTextRect) {
        if (dc.isTextFormat() && TxtUtils.isFooterNote(AppState.get().selectedText)) {
            DragingDialogs.dialogFooterNotes(anchor, dc, new Runnable() {
                @Override
                public void run() {
                    showHideHistory();
                }
            });
        } else {
            Log.d("测试文本选中", "长按");
            if (AppState.get().isRememberDictionary) {
                DictsHelper.runIntent(dc.getActivity(), anchor, AppState.get().selectedText);
                dc.clearSelectedText();
            } else {
                // 加载app上次保存的颜色
                AppBook appBook = SharedBooks.load(dc.getBookFileMeta().getPath());
                if (appBook.lastMarkColor == -1 || appBook.lastMarkType == -1) {
                    if (anchorX.getVisibility() == View.GONE) {
                        // 重置坐标
                        AnchorHelper.setXY(anchorX, 0, 0);
                        AnchorHelper.setXY(anchorY, 0, 0);
                        // 设置anchorX,Y坐标
                        AnchorHelper.setXY(
                                anchorX,
                                selectedTextRect.left - anchorX.getWidth(),
                                selectedTextRect.top + anchorX.getHeight()
                        );
                        AnchorHelper.setXY(
                                anchorY,
                                selectedTextRect.right,
                                selectedTextRect.bottom + Dips.DP_40
                        );
                        anchorX.setVisibility(View.VISIBLE);
                        anchorY.setVisibility(View.VISIBLE);
                    }
                    DragingDialogs.dialogSelectText(anchorX, anchorY, dc, (int) selectedTextRect.width(), true, updateUIRunnable);
                } else {
                    // 直接标注选中的文本
                    dc.underlineText(appBook.lastMarkColor, 2.0f, AnnotationType.getAnnotationType(appBook.lastMarkType));
                }
            }
        }
    }

    public void showSelectTextMenu() {
        Log.d("测试文本选中", "showSelectTextMenu");
        DragingDialogs.dialogSelectText(anchorX, anchorY, dc, 0, true, updateUIRunnable);

    }

    Runnable updateTimePower = new Runnable() {

        @Override
        public void run() {
//            handlerTimer.postDelayed(updateTimePower, AppState.APP_UPDATE_TIME_IN_UI);
        }
    };

    public boolean checkBack(final KeyEvent event) {
        int keyCode = event.getKeyCode();
        if (keyCode == 0) {
            keyCode = event.getScanCode();
        }

        if (anchor == null) {
            closeAndRunList();
            return true;
        }
        if (AppState.get().isAutoScroll) {
            AppState.get().isAutoScroll = false;
            updateUI();
            return true;
        }

        if (dc.floatingBookmark != null) {
            dc.floatingBookmark = null;
            onRefresh.run();
            return true;
        }

        if (KeyEvent.KEYCODE_BACK == keyCode) {
            if (closeDialogs()) {
                return true;
            } else if (!dc.getLinkHistory().isEmpty()) {
                dc.onLinkHistory();
                return true;
            }
        }
        return false;
    }

//    public boolean dispatchKeyEventUp(final KeyEvent event) {
//        int keyCode = event.getKeyCode();
//        if (keyCode == 0) {
//            keyCode = event.getScanCode();
//        }
//
//        if (KeyEvent.KEYCODE_MENU == keyCode || KeyEvent.KEYCODE_M == keyCode) {
//            doShowHideWrapperControlls();
//            return true;
//        }
//
//        return false;
//
//    }

    public boolean dispatchKeyEventDown(final KeyEvent event) {
        int keyCode = event.getKeyCode();
        if (keyCode == 0) {
            keyCode = event.getScanCode();
        }

        if (AppState.get().isUseVolumeKeys && AppState.get().getNextKeys().contains(keyCode)) {
            if (closeDialogs()) {
                return true;
            }
            nextChose(false, event.getRepeatCount());
            return true;
        }

        if (AppState.get().isUseVolumeKeys && AppState.get().getPrevKeys().contains(keyCode)) {
            if (closeDialogs()) {
                return true;
            }
            prevChose(false, event.getRepeatCount());
            return true;
        }

        return false;
    }

    public void closeAndRunList() {
        EventBus.getDefault().unregister(this);

        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }

        if (titleBar != null) {
            titleBar.removeCallbacks(null);
        }
        dc.saveCurrentPageAsync();
        dc.onCloseActivityAdnShowInterstial();
        dc.closeActivity();

    }

//    View.OnClickListener onTextToSpeach = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View v) {
//            if (AppSP.get().isCut) {
//                onModeChange.setImageResource(R.drawable.my_glyphicons_two_page_one);
//                onCut.onClick(null);
//                return;
//            }
//            DragingDialogs.dialogTextToSpeech(anchor, dc);
//        }
//    };

    public void updateSpeedLabel() {

//        Info info = OutlineHelper.getForamtingInfo(dc, true);
        LOG.d("文档页面数", dc.getCurentPage() + "/" + dc.getPageCount());
        pagesCountIndicator.setText(dc.getCurentPage() + "/" + dc.getPageCount());
        double percentageValue = (double) (dc.getCurentPage()) / dc.getPageCount() * 100;
        percent.setText(String.format("%.2f%%", percentageValue));
    }

    public void updateUI() {
        updateSpeedLabel();
        showChapter();
        checkBookmarkStatus(dc.getCurentPage());
        hideShow();
        initNextType();
        initToolBarPlusMinus();
        showHideHistory();

        updateLock();


//        if (dc.isTextFormat()) {
////            crop.setVisibility(View.GONE);
////            cut.setVisibility(View.GONE);
//            if (Dips.isEInk() || AppState.get().appTheme == AppState.THEME_INK || AppState.get().isEnableBC) {
//                onBC.setVisibility(View.VISIBLE);
//            } else {
//                onBC.setVisibility(View.GONE);
//            }
//            if (AppSP.get().isCrop) {
//                crop.setVisibility(View.VISIBLE);
//            }
//            if (AppSP.get().isCut) {
//                cut.setVisibility(View.VISIBLE);
//            }
//        }

//        crop.underline(AppSP.get().isCrop);
//        cut.underline(AppSP.get().isCut);

        if (AppState.get().inactivityTime > 0) {
            dc.getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            LOG.d("FLAG addFlags", "FLAG_KEEP_SCREEN_ON", "add", AppState.get().inactivityTime);
            handler.removeCallbacks(clearFlags);
            handler.postDelayed(clearFlags, TimeUnit.MINUTES.toMillis(AppState.get().inactivityTime));
        }

//        if (AppState.get().isAutoScroll) {
//            pagesBookmark.setVisibility(View.GONE);
//        } else {
//            pagesBookmark.setVisibility(View.VISIBLE);
//        }


        dc.saveCurrentPage();
        //SharedBooks.save(bs);

        LOG.d("dc.floatingBookmark", dc.floatingBookmark);
        if (dc.floatingBookmark != null) {
            dc.floatingBookmark.p = dc.getPercentage();
            floatingBookmarkTextView.setText("{" + dc.getCurentPageFirst1() + "}");
            floatingBookmarkTextView.setVisibility(View.VISIBLE);

            BookmarksData.get().add(dc.floatingBookmark);
            showPagesHelper();
        } else {
            floatingBookmarkTextView.setVisibility(View.GONE);
        }

        try {
            if (!dc.isTextFormat()) {
                TempHolder.get().documentTitleBarHeight = documentTitleBar.getHeight();
            } else {
                TempHolder.get().documentTitleBarHeight = 0;
            }
        } catch (Exception e) {
            TempHolder.get().documentTitleBarHeight = 0;
        }

        final int dashHeight = Math.min(Dips.dpToPx(220), Dips.screenHeight() / 3);
        line1.getLayoutParams().height = dashHeight;
        line2.getLayoutParams().height = dashHeight;

        line1.setLayoutParams(line1.getLayoutParams());
        line2.setLayoutParams(line2.getLayoutParams());

    }

    public void hideShowPrevNext() {
        if (dc.isMusicianMode()) {
            if (AppState.get().isShowRectangularTapZones) {
                line1.setVisibility(View.VISIBLE);
                line2.setVisibility(View.VISIBLE);
            } else {
                line1.setVisibility(View.GONE);
                line2.setVisibility(View.GONE);

            }
        }
    }

    public void showChapter() {
        String text = dc.getCurrentChapter();
        if (TxtUtils.isNotEmpty(text)) {
            chapterTitle.setText(text.trim());
            catalogTitle.setText(chapterTitle.getText());
        }
    }

    public void updateLock() {
        // int mode = View.VISIBLE;

        if (AppSP.get().isLocked) {
//            lockUnlock.setImageResource(R.drawable.glyphicons_217_lock);
//            lockUnlockTop.setImageResource(R.drawable.glyphicons_217_lock);
            // lockUnlock.setColorFilter(a.getResources().getColor(R.color.tint_yellow));
            // lockUnlockTop.setColorFilter(a.getResources().getColor(R.color.tint_yellow));
            // mode = View.VISIBLE;
        } else {
//            lockUnlock.setImageResource(R.drawable.glyphicons_218_lock_open);
//            lockUnlockTop.setImageResource(R.drawable.glyphicons_218_lock_open);
            // lockUnlock.setColorFilter(a.getResources().getColor(R.color.tint_white));
            // lockUnlockTop.setColorFilter(a.getResources().getColor(R.color.tint_white));
            // mode = View.GONE;
        }
        //TxtUtils.updateAllLinks(parentParent);
//        if (AppState.get().l) {
//            TintUtil.setTintImageWithAlpha(moveCenter, Color.LTGRAY);
//        } else {
//            TintUtil.setTintImageWithAlpha(moveCenter, Color.WHITE);
//        }

    }

    public void showHideHistory() {
//        linkHistory.setVisibility(dc.getLinkHistory().isEmpty() ? View.GONE : View.VISIBLE);
    }

    @Subscribe
    public void showHideTextSelectors(MessagePageXY event) {
        Log.d("测试", "显示文本选择工具2");
        if (event.getType() == MessagePageXY.TYPE_HIDE) {
            anchorX.setVisibility(View.GONE);
            anchorY.setVisibility(View.GONE);

        }
        if (event.getType() == MessagePageXY.TYPE_SHOW) {
            anchorX.setVisibility(View.VISIBLE);
            anchorY.setVisibility(View.VISIBLE);

            AnchorHelper.setXY(anchorX, event.getX(), event.getY());
            AnchorHelper.setXY(anchorY, event.getX1(), event.getY1());

        }

    }

    Runnable onRefresh = new Runnable() {

        @Override
        public void run() {
            LOG.d("测试run", "runRefresh");
            dc.saveCurrentPageAsync();
            initToolBarPlusMinus();
            updateSeekBarColorAndSize();
            hideShow();
            updateUI();
//            TTSEngine.get().stop();
            BrightnessHelper.updateOverlay(overlay);
            showPagesHelper();
            hideShowPrevNext();
        }
    };

    private void setSidebarWidth(boolean half) {
        ViewGroup.LayoutParams layoutParams = sidebar.getLayoutParams();
        layoutParams.width = half ? Dips.screenWidth() / 2 : Dips.screenWidth();
        sidebar.setLayoutParams(layoutParams);
    }



    public void initUI(final Activity a) {
        this.a = a;
        quickBookmark = a.getString(R.string.fast_bookmark);
        drawerLayout = a.findViewById(R.id.drawer_layout);
        drawerLayout.addDrawerListener(new DrawerLayout.SimpleDrawerListener() {
            @Override
            public void onDrawerOpened(View drawerView) {
                drawerView.setClickable(true);
            }

            @Override
            public void onDrawerClosed(View drawerView) {
                super.onDrawerClosed(drawerView);
            }
        });


        sidebar = a.findViewById(R.id.sidebar);
        setSidebarWidth(true);
        chapterMenuView = a.findViewById(R.id.chapter_menu);
        chapterMenu = new ChapterMenu(chapterMenuView, dc, () -> {
            drawerLayout.closeDrawer(GravityCompat.START);
            return Unit.INSTANCE;
        }, link -> {
            if (link.targetPage != -1) {
                int pageCount = dc.getPageCount();
                if (link.targetPage < 1 || link.targetPage > pageCount) {
                    Toast.makeText(anchor.getContext(), "Error Chapter", Toast.LENGTH_SHORT).show();
                } else {
                    dc.onGoToPage(link.targetPage);
                    drawerLayout.closeDrawer(GravityCompat.START);
                    // 隐藏顶部和底部工具栏
                    AppState.get().isEditMode = false;
                    hide();
                    initToolBarPlusMinus();
                }
            }
            return Unit.INSTANCE;
        }, half -> {
            setSidebarWidth(half);
            return Unit.INSTANCE;
        }, deleteBookmark -> {
            checkBookmarkStatus(dc.getCurentPage());
            return Unit.INSTANCE;
        }, () -> {
            checkBookmarkStatus(dc.getCurentPage());
            return Unit.INSTANCE;
        });
        menuLayout = a.findViewById(R.id.menuLayout);
        bottomBar = a.findViewById(R.id.bottomBar1);

        initHeader();
        initProgress();
        initFooterStyle();
//        a.findViewById(R.id.showHypenLangPanel).setVisibility(View.GONE);

        parentParent = a.findViewById(R.id.parentParent);
        documentTitleBar = a.findViewById(R.id.document_title_bar);
//        linkHistory = (ImageView) a.findViewById(R.id.linkHistory);
//        linkHistory.setOnClickListener(onLinkHistory);

        anchor = (FrameLayout) a.findViewById(R.id.anchor);

        anchorX = (ImageView) a.findViewById(R.id.anchorX);
        anchorY = (ImageView) a.findViewById(R.id.anchorY);

        floatingBookmarkTextView = a.findViewById(R.id.floatingBookmark);
        floatingBookmarkTextView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dc.floatingBookmark = null;
                onRefresh.run();
                onBookmarks.onClick(v);
            }
        });
        floatingBookmarkTextView.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                dc.floatingBookmark = null;
                onRefresh.run();
                return true;
            }
        });

        TintUtil.setTintImageWithAlpha(anchorX, AppState.get().isDayNotInvert ? Color.BLUE : Color.YELLOW, 150);
        TintUtil.setTintImageWithAlpha(anchorY, AppState.get().isDayNotInvert ? Color.BLUE : Color.YELLOW, 150);

        anchorX.setVisibility(View.GONE);
        anchorY.setVisibility(View.GONE);

        DraggbleTouchListener touch1 = new DraggbleTouchListener(anchorX, (View) anchorX.getParent());
        DraggbleTouchListener touch2 = new DraggbleTouchListener(anchorY, (View) anchorY.getParent());

        final Runnable onMoveActionOnce = new Runnable() {

            @Override
            public void run() {
                float x = anchorX.getX() + anchorX.getWidth();
                float y = anchorX.getY() + anchorX.getHeight() / 2;

                float x1 = anchorY.getX();
                float y1 = anchorY.getY();
                EventBus.getDefault().post(new MessagePageXY(MessagePageXY.TYPE_SELECT_TEXT, dc.getCurentPage(), x, y, x1, y1));
            }
        };
        final Runnable onMoveAction = new Runnable() {

            @Override
            public void run() {
                handler.removeCallbacks(onMoveActionOnce);
                handler.postDelayed(onMoveActionOnce, 150);

            }
        };

        Runnable onMoveFinish = new Runnable() {
            @Override
            public void run() {
                onMoveAction.run();
                // 加载app上次保存的颜色
                AppBook appBook = SharedBooks.load(dc.getBookFileMeta().getPath());
                Log.d("测试文本选中", "onMoveFinish");
                if (appBook.lastMarkColor == -1 || appBook.lastMarkType == -1) {
                    if (AppState.get().isRememberDictionary) {
                        final String text = AppState.get().selectedText;
                        DictsHelper.runIntent(dc.getActivity(), anchor, text);
                        dc.clearSelectedText();
                    } else {
                        DragingDialogs.dialogSelectText(anchorX, anchorY, dc, 0, true, updateUIRunnable);
                    }
                } else {
                    dc.underlineText(appBook.lastMarkColor, 2.0f, AnnotationType.getAnnotationType(appBook.lastMarkType));
                }
            }
        };

        touch1.setOnMoveFinish(onMoveFinish);
        touch2.setOnMoveFinish(onMoveFinish);

        touch1.setOnMove(onMoveAction);
        touch2.setOnMove(onMoveAction);

        titleBar = a.findViewById(R.id.titleBar);
        titleBar.setOnClickListener(onMenu);

        overlay = a.findViewById(R.id.overlay);
        overlay.setVisibility(View.VISIBLE);

        line1 = a.findViewById(R.id.line1);
        line1.setOnClickListener(onPrevPage);

        line2 = a.findViewById(R.id.line2);
        line2.setOnClickListener(onNextPage);

        lineClose = a.findViewById(R.id.lineClose);
        lineClose.setOnClickListener(onClose);


        lineFirst = a.findViewById(R.id.lineFirst);
        lineFirst.setOnClickListener(onGoToPAge1);

//        lirbiLogo = (TextView) a.findViewById(R.id.lirbiLogo);
//        lirbiLogo.setText(AppState.get().musicText);
//        lirbiLogo.setOnClickListener(onLirbiLogoClick);

//        editTop2 = (ImageView) a.findViewById(R.id.editTop2);
//        editTop2.setOnClickListener(onShowHideEditPanel);


        // nextPage.setOnClickListener(onNextPage);
        // prevPage.setOnClickListener(onPrevPage);
        // if (Dips.isEInk(dc.getActivity())) {
        // brightness.setVisibility(View.GONE);
        // AppState.get().isDayNotInvert = true;
        // }

//        onBC = (UnderlineImageView) a.findViewById(R.id.onBC);
//        onBC.setOnClickListener(onBCclick);
//        onBC.underline(AppState.get().isEnableBC);


//        onTextReplacement = a.findViewById(R.id.onTextReplacement);


        onCloseBook = a.findViewById(R.id.close);
        Apps.accessibilityButtonSize(onCloseBook);
        onCloseBook.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        onCloseBook.setOnClickListener(onClose);
        onCloseBook.setOnLongClickListener(onCloseLongClick);
        onCloseBook.setVisibility(View.INVISIBLE);

        bookmark2 = a.findViewById(R.id.onBookmark2);
        share = a.findViewById(R.id.onShare);

        bookmark2.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        share.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        onCloseBook.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));

        bookmark2.setOnClickListener((v) -> {
            handler.post(addBookmark);
        });
        share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ExtUtils.sendFileTo(a, dc.getCurrentBook());
                doShowHideWrapperControlls();
            }
        });
//        showSearch = (ImageView) a.findViewById(R.id.onShowSearch);
//        showSearch.setOnClickListener(onShowSearch);
//        autoScroll = ((ImageView) a.findViewById(R.id.autoScroll));
//        autoScroll.setOnClickListener(onAutoScroll);

        // ((View)
        // a.findViewById(R.id.onScreenMode)).setOnClickListener(onScreenMode);

//        nextTypeBootom = (TextView) a.findViewById(R.id.nextTypeBootom);

//        nextTypeBootom.setOnClickListener(onNextType);


//        onDocDontext = (ImageView) a.findViewById(R.id.onDocDontext);
//        onDocDontext.setOnClickListener(onShowContext);

//        lockUnlock = (ImageView) a.findViewById(R.id.lockUnlock);

//        ttsActive = a.findViewById(R.id.ttsActive);
//        ttsActive.setDC(dc);
//        ttsActive.addOnDialogRunnable(new Runnable() {
//
//            @Override
//            public void run() {
//                AppState.get().isEditMode = true;
//                hideShow();
//                DragingDialogs.dialogTextToSpeech(anchor, dc);
//            }
//        });


//        textToSpeach = (ImageView) a.findViewById(R.id.textToSpeach);
//        textToSpeach.setOnClickListener(onTextToSpeach);
//        textToSpeach.setOnLongClickListener(v -> {
//            AlertDialogs.showTTSDebug(dc);
//            hideShow();
//            return true;
//        });

        drawView = (DrawView) a.findViewById(R.id.drawView);

//        View bookmarks = a.findViewById(R.id.onBookmarks);
//        bookmarks.setOnClickListener(onBookmarks);
//        bookmarks.setOnLongClickListener(onBookmarksLong);

//        toastBrightnessText = (TextView) a.findViewById(R.id.toastBrightnessText);
//        toastBrightnessText.setVisibility(View.GONE);
//        TintUtil.setDrawableTint(toastBrightnessText.getCompoundDrawables()[0], Color.WHITE);

//        TextView modeName = (TextView) a.findViewById(R.id.modeName);

//        if (AppState.get().isEnableAccessibility) {
//            modeName.setText(AppState.get().nameVerticalMode + " (" + dc.getString(R.string.accessibility) + ")");
//        } else {
//            modeName.setText(AppState.get().nameVerticalMode);
//        }

        pagesCountIndicator = (TextView) a.findViewById(R.id.currentPageIndex);
        percent = a.findViewById(R.id.percent);

//        currentSeek = (TextView) a.findViewById(R.id.currentSeek);
//        maxSeek = (TextView) a.findViewById(R.id.maxSeek);

//        currentSeek.setOnLongClickListener(new OnLongClickListener() {
//
//            @Override
//            public boolean onLongClick(View v) {
//                Dialogs.showDeltaPage(anchor, dc, dc.getCurentPageFirst1(), updateUIRunnable);
//                return true;
//            }
//        });
//        maxSeek.setOnLongClickListener(new OnLongClickListener() {
//
//            @Override
//            public boolean onLongClick(View v) {
//                Dialogs.showDeltaPage(anchor, dc, dc.getCurentPageFirst1(), updateUIRunnable);
//                return true;
//            }
//        });

//        View thumbnail = a.findViewById(R.id.thumbnail);
//        thumbnail.setOnClickListener(onThumbnail);

//        View bookMenu = a.findViewById(R.id.bookMenu);
//        bookMenu.setOnClickListener(onItemMenu);
//        modeName.setOnClickListener(onItemMenu);
//        modeName.setOnLongClickListener(onCloseLongClick);
//        modeName.setOnLongClickListener(new OnLongClickListener() {
//
//            @Override
//            public boolean onLongClick(View v) {
//                dc.onChangeTextSelection();
//                AppState.get().isEditMode = false;
//                hideShow();
//                return true;
//            }
//        });


        AppState.get().isAutoScroll = false;

//        ImageView recent = (ImageView) a.findViewById(R.id.onRecent);
//        recent.setOnClickListener(onRecent);

        anchor.getViewTreeObserver().addOnGlobalLayoutListener(new OnGlobalLayoutListener() {

            @Override
            public void onGlobalLayout() {
                if (anchor.getX() < 0) {
                    anchor.setX(0);
                }
                if (anchor.getY() < 0) {
                    anchor.setY(0);
                }
            }

        });
        updateSeekBarColorAndSize();
        BrightnessHelper.updateOverlay(overlay);

        // bottom 1
        TintUtil.setStatusBarColor(a);


        line1.setVisibility(View.GONE);
        line2.setVisibility(View.GONE);
        lineFirst.setVisibility(View.GONE);
        lineClose.setVisibility(View.GONE);


        overlay2 = a.findViewById(R.id.overlay2);
        overlay2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 如果进度面板或样式面板显示，则不关闭菜单栏
                if (progressLayout.getVisibility() == View.VISIBLE || styleLayout.getVisibility() == View.VISIBLE) {
                    return;
                }
                AppState.get().isEditMode = false;
                hideShow();
            }
        });
        // 初始化底部按钮
        footerCatalog = a.findViewById(R.id.footer_catalog);
        footerProgress = a.findViewById(R.id.footer_progress);
        footerStyle = a.findViewById(R.id.footer_style);
        footerReflow = a.findViewById(R.id.footer_reflow);

        // 初始化底部按钮图标
        footerCatalogImage = a.findViewById(R.id.footer_catalog_image);
        footerProgressImage = a.findViewById(R.id.footer_progress_image);
        footerProgressImage.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        footerStyleImage = a.findViewById(R.id.footer_style_image);
        footerReflowImage = a.findViewById(R.id.footer_reflow_image);

        // 初始化底部按钮文本
        footerCatalogText = a.findViewById(R.id.footer_catalog_text);
        footerProgressText = a.findViewById(R.id.footer_progress_text);
        footerStyleText = a.findViewById(R.id.footer_style_text);
        footerReflowText = a.findViewById(R.id.footer_reflow_text);

        // 初始化内容布局
//        catalogLayout =  a.findViewById(R.id.);
        progressLayout = a.findViewById(R.id.progress_layout);
        styleLayout = a.findViewById(R.id.style_layout);

        // 设置点击监听器
        footerCatalog.setOnClickListener(v -> {
            selectCatalog(a);
            if (chapterMenu.isNeedLoadContent()) {
                chapterMenu.loadContentList();
            } else {
                chapterMenu.setCurMenu(OutlineHelper.getCurrentChapter(dc));
            }
            chapterMenu.loadData();
            // 隐藏顶部和底部工具栏
            AppState.get().isEditMode = false;
            hide();
            drawerLayout.openDrawer(GravityCompat.START);
        });
        footerProgress.setOnClickListener(v -> selectProgress(a));
        footerStyle.setOnClickListener(v -> selectStyle(a));
        footerReflow.setOnClickListener(v -> selectReflow(a));
        hide();
//        currentSeek.setVisibility(View.GONE);
//        maxSeek.setVisibility(View.GONE);
//        seekBar.setVisibility(View.INVISIBLE);

//        hideShowPrevNext();
        dc.initAnchor(anchor);
        TxtUtils.updateAllLinks(parentParent);
    }


    public boolean hasCurrentPageBookmark(int page) {
        return BookmarksData.get().hasBookmark(dc.getCurrentBook().getPath(), page, dc.getPageCount());
    }

    public boolean hasCurrentPageBookmark() {
        return BookmarksData.get().hasBookmark(dc.getCurrentBook().getPath(), dc.getCurentPage(), dc.getPageCount());
    }


    private Runnable addBookmark = new Runnable() {
        @Override
        public void run() {
            if (!hasCurrentPageBookmark()) {
                // 添加
                AppBookmark mark = new AppBookmark(
                        dc.getCurrentBook().getPath(),
                        dc.getPageFirstParagraph(dc.getCurentPage() - 1),
                        (float) dc.getCurentPage() / dc.getPageCount());
                mark.title = OutlineHelper.getCurrentChapterAndParentAsString(dc);
                BookmarksData.get().add(mark);
                bookmarkBtn.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark_selected));
                bookmarkBtn.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
                bookmark2.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark_selected));
                bookmark2.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
            } else {
                // 移除
                AppBookmark mark = BookmarksData.get().getBookmarkByBook(dc.getCurrentBook().getPath(), dc.getCurentPage(), dc.getPageCount());
                BookmarksData.get().remove(mark);
                bookmarkBtn.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark));
                bookmarkBtn.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
                bookmark2.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark));
                bookmark2.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
            }
        }
    };

    private void initHeader() {
        chapterTitle = a.findViewById(R.id.chapter_title);
        bookmarkBtn = a.findViewById(R.id.bookmarkbtn);
        bookmarkBtn.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        bookmarkBtn.setOnClickListener(v -> {
            handler.post(addBookmark);
        });
    }


    private void initProgress() {
        catalogTitle = a.findViewById(R.id.catalog_title);
        progressPrev = a.findViewById(R.id.progress_prev);
        progressNext = a.findViewById(R.id.progress_next);

        progressSeekBar = a.findViewById(R.id.progress_seekbar);
        progressSeekBar.setProgress(dc.getCurentPage());
        progressSeekBar.setMax(dc.getPageCount());
        // 获取 thumb 的 Drawable
        Drawable thumbDrawable = progressSeekBar.getThumb();

        thumbDrawable = DrawableCompat.wrap(thumbDrawable);
        DrawableCompat.setTint(thumbDrawable, ContextCompat.getColor(a, R.color.text_primary));
        progressSeekBar.setThumb(thumbDrawable);
        progressPrev.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        progressNext.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));

        catalogCurProgress = a.findViewById(R.id.catalog_cur_progress);
        catalogCurProgress.setText(progressSeekBar.getProgress() + "/" + progressSeekBar.getMax());

        // 上一页按钮点击事件
        progressPrev.setOnClickListener(v -> {
            prevChose(false);
            catalogTitle.setText(dc.getCurrentChapter());
        });

        // 下一页按钮点击事件
        progressNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                nextChose(false);
                catalogTitle.setText(dc.getCurrentChapter());
            }
        });

        // 进度条变化监听
        progressSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    catalogTitle.setText(dc.getCurrentChapter());
                    catalogCurProgress.setText(progress + "/" + progressSeekBar.getMax());
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // 开始拖动时的处理
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // 结束拖动时的处理
                // 用户拖动进度条时的处理
                dc.onGoToPage(seekBar.getProgress());
                checkBookmarkStatus(seekBar.getProgress());
                catalogCurProgress.setText(progressSeekBar.getProgress() + "/" + progressSeekBar.getMax());
                catalogTitle.setText(dc.getCurrentChapter());
                doShowHideWrapperControlls();
            }
        });
    }


    private void initFooterStyle() {
        // 字体大小组件
        fontSizeDecrease = a.findViewById(R.id.font_size_decrease);
        fontSizeSeekBar = a.findViewById(R.id.font_size_seekbar);
        fontSizeIncrease = a.findViewById(R.id.font_size_increase);
        fontSizeValue = a.findViewById(R.id.font_size_value);
        fontSizeDecrease.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        fontSizeIncrease.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        fontSizeSeekBar.setMax(36);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            fontSizeSeekBar.setMin(10);
        }
        fontSizeSeekBar.setProgress(BookCSS.get().fontSizeSp);
        fontSizeValue.setText(BookCSS.get().fontSizeSp + "");

        // 文本增强组件
        textEnhancementDecrease = a.findViewById(R.id.text_enhancement_decrease);
        textEnhancementSeekBar = a.findViewById(R.id.text_enhancement_seekbar);
        textEnhancementIncrease = a.findViewById(R.id.text_enhancement_increase);
        textEnhancementValue = a.findViewById(R.id.text_enhancement_value);
        textEnhancementDecrease.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        textEnhancementIncrease.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        textEnhancementSeekBar.setMax(900);
        textEnhancementSeekBar.setProgress(BookCSS.get().fontWeight);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            textEnhancementSeekBar.setMin(100);
        }
        textEnhancementValue.setText(BookCSS.get().fontWeight + "");
        // 裁剪组件
        fitScreen = a.findViewById(R.id.fit_screen);
        fitScreenImage = a.findViewById(R.id.fit_screen_image);
        fitScreenText = a.findViewById(R.id.fit_screen_text);
        fitWidth = a.findViewById(R.id.fit_width);
        fitWidthImage = a.findViewById(R.id.fit_width_image);
        fitWidthText = a.findViewById(R.id.fit_width_text);
        styleCrop = a.findViewById(R.id.style_crop);
        styleCropImage = a.findViewById(R.id.style_crop_image);
        styleCropText = a.findViewById(R.id.style_crop_text);
        styleReset = a.findViewById(R.id.style_reset);
        styleResetImage = a.findViewById(R.id.style_reset_image);
        styleResetText = a.findViewById(R.id.style_reset_text);
        resetCropSelection();
        if (AppSP.get().crop == 1) {
            setFooterSelectStyle(fitScreenImage, fitScreenText, true);
        } else if (AppSP.get().crop == 2) {
            setFooterSelectStyle(fitWidthImage, fitWidthText, true);
        } else if (AppSP.get().crop == 3) {
            setFooterSelectStyle(styleCropImage, styleCropText, true);
        } else if (AppSP.get().crop == 4) {
            setFooterSelectStyle(styleResetImage, styleResetText, true);
        }

        // 翻页样式组件
        pageTurnFlip = a.findViewById(R.id.page_turn_flip);
        pageTurnFlipImage = a.findViewById(R.id.page_turn_flip_image);
        pageTurnFlipText = a.findViewById(R.id.page_turn_flip_text);
        pageTurnSlide = a.findViewById(R.id.page_turn_slide);
        pageTurnSlideImage = a.findViewById(R.id.page_turn_slide_image);
        pageTurnSlideText = a.findViewById(R.id.page_turn_slide_text);
        pageTurnScroll = a.findViewById(R.id.page_turn_scroll);
        pageTurnScrollImage = a.findViewById(R.id.page_turn_scroll_image);
        pageTurnScrollText = a.findViewById(R.id.page_turn_scroll_text);
        resetPageTurnSelection();

        // 页面缩放组件
        pageZoomIn = a.findViewById(R.id.page_zoom_in);
        pageZoomInImage = a.findViewById(R.id.page_zoom_in_image);
        pageZoomInText = a.findViewById(R.id.page_zoom_in_text);
        pageZoomOut = a.findViewById(R.id.page_zoom_out);
        pageZoomOutImage = a.findViewById(R.id.page_zoom_out_image);
        pageZoomOutText = a.findViewById(R.id.page_zoom_out_text);
        pageZoomInImage.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        pageZoomOutImage.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));


        // 字体大小减少按钮点击事件
        fontSizeDecrease.setOnClickListener(v -> {
            if (BookCSS.get().fontSizeSp < 10) return;
            updateFontSize(BookCSS.get().fontSizeSp - 1);
            onRefresh.run();
            dc.restartActivity();
        });

        // 字体大小增加按钮点击事件
        fontSizeIncrease.setOnClickListener(v -> {
            if (BookCSS.get().fontSizeSp > 36) return;
            // 处理字体大小增加逻辑
            updateFontSize(BookCSS.get().fontSizeSp + 1);
            onRefresh.run();
            dc.restartActivity();
        });

        // 字体大小滑动条变化事件
        fontSizeSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                // 更新字体大小值
                updateFontSize(progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                onRefresh.run();
                dc.restartActivity();
            }
        });

        // 文本增强相关监听器（类似字体大小的设置）
        textEnhancementDecrease.setOnClickListener(v -> {
            if (BookCSS.get().fontSizeSp <= 100) return;
            updateFontWeight(BookCSS.get().fontWeight - 100);
            onRefresh.run();
            dc.restartActivity();
        });
        textEnhancementIncrease.setOnClickListener(v -> {
            if (BookCSS.get().fontSizeSp >= 900) return;
            updateFontWeight(BookCSS.get().fontWeight + 100);
            onRefresh.run();
            dc.restartActivity();
        });
        textEnhancementSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
//                updateTextEnhancementValue(progress);
                updateFontWeight(progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                onRefresh.run();
                dc.restartActivity();
            }
        });

        // 裁剪选项点击事件
        fitScreen.setOnClickListener(v -> {
            if (AppSP.get().crop == 1) return;
            AppSP.get().crop = 1;
            dc.alignDocument();
            resetCropSelection();
            setFooterSelectStyle(fitScreenImage, fitScreenText, true);
            AppSP.get().save();
        });
        fitWidth.setOnClickListener(v -> {
            if (AppSP.get().crop == 2) return;
            AppSP.get().crop = 2;
            dc.centerHorizontal();
            resetCropSelection();
            setFooterSelectStyle(fitWidthImage, fitWidthText, true);
            AppSP.get().save();
        });

        styleCrop.setOnClickListener(v -> {
            if (AppSP.get().crop == 3) return;
            AppSP.get().crop = 3;
            AppSP.get().isCrop = true;
            dc.onCrop();
            updateUI();
            AppState.get().isEditMode = false;
            hideShow();
            hideShowEditIcon();
            resetCropSelection();
            setFooterSelectStyle(styleCropImage, styleCropText, true);
            AppSP.get().save();
        });
        styleReset.setOnClickListener(v -> {
            if (AppSP.get().crop == 4) return;
            AppSP.get().crop = 4;
            AppSP.get().isCrop = false;
            dc.onCrop();
            updateUI();
            AppState.get().isEditMode = false;
            hideShow();
            hideShowEditIcon();
            resetCropSelection();
            setFooterSelectStyle(styleResetImage, styleResetText, true);
            AppSP.get().save();
        });

        // 翻页样式点击事件
        pageTurnFlip.setOnClickListener(v -> {
//            if (AppSP.get().pageTurnStyle == 1) return;
            AppBook appBook = SharedBooks.load(dc.getBookFileMeta().getPath());
            if (appBook.animationType == 3) return;
            Toast.makeText(a, "目前不支持该操作", Toast.LENGTH_SHORT).show();
//            AppSP.get().pageTurnStyle = 1;
            setFooterSelectStyle(pageTurnFlipImage, pageTurnFlipText, true);
//            animationSwitchListener.onAnimationSwitch(DocumentViewMode.CURL);
            appBook.animationType = 3;
            SharedBooks.save(appBook);
            dc.onCloseActivityFinal(new Runnable() {
                @Override
                public void run() {
                    AppSP.get().readingMode = AppState.READING_MODE_FLIP;
                    AppSP.get().save();
                    ExtUtils.showDocumentWithoutDialog(a, dc.getCurrentBook(), a.getIntent().getStringExtra(DocumentController.EXTRA_PLAYLIST));
                }
            });
        });
        pageTurnSlide.setOnClickListener(v -> {
            AppBook appBook = SharedBooks.load(dc.getBookFileMeta().getPath());
            if (appBook.animationType == 2) return;
//            Toast.makeText(a, "目前不支持该操作", Toast.LENGTH_SHORT).show();
            setFooterSelectStyle(pageTurnSlideImage, pageTurnSlideText, true);
            appBook.animationType = 2;
            SharedBooks.save(appBook);
            dc.onCloseActivityFinal(new Runnable() {
                @Override
                public void run() {
                    AppSP.get().readingMode = AppState.READING_MODE_SCROLL;
                    AppSP.get().save();
                    ExtUtils.showDocumentWithoutDialog(a, dc.getCurrentBook(), a.getIntent().getStringExtra(DocumentController.EXTRA_PLAYLIST));
//                    dc.restartActivity();
                }
            });
        });
        pageTurnScroll.setOnClickListener(v -> {
            AppBook appBook = SharedBooks.load(dc.getBookFileMeta().getPath());
            if (appBook.animationType == 1) return;
//            AppSP.get().pageTurnStyle = 3;
//            Toast.makeText(a, "目前不支持该操作", Toast.LENGTH_SHORT).show();
            setFooterSelectStyle(pageTurnScrollImage, pageTurnScrollText, true);
            appBook.animationType = 1;
            SharedBooks.save(appBook);
            dc.onCloseActivityFinal(new Runnable() {
                @Override
                public void run() {
                    AppSP.get().readingMode = AppState.READING_MODE_SCROLL;
                    AppSP.get().save();
                    ExtUtils.showDocumentWithoutDialog(a, dc.getCurrentBook(), a.getIntent().getStringExtra(DocumentController.EXTRA_PLAYLIST));
//                    dc.restartActivity();
                }
            });
//            dc.onCloseActivityFinal(new Runnable() {
//                @Override
//                public void run() {
//                    AppSP.get().readingMode = AppState.READING_MODE_SCROLL;
//                    AppSP.get().save();
//                    ExtUtils.showDocumentWithoutDialog(a, dc.getCurrentBook(), a.getIntent().getStringExtra(DocumentController.EXTRA_PLAYLIST));
//                }
//            });
        });
//        // 页面缩放点击事件
        pageZoomIn.setOnClickListener(v -> {
            dc.onZoomInc();
        });
        pageZoomOut.setOnClickListener(v -> {
            dc.onZoomDec();
        });
    }

    // 重置裁剪选项的选中状态
    private void resetCropSelection() {
        setFooterSelectStyle(fitScreenImage, fitScreenText, false);
        setFooterSelectStyle(fitWidthImage, fitWidthText, false);
        setFooterSelectStyle(styleCropImage, styleCropText, false);
        setFooterSelectStyle(styleResetImage, styleResetText, false);
    }

    // 设置裁剪模式的选中状态
    private void setFooterSelectStyle(ImageView imageView, TextView textView, boolean isSelected) {
        int tintColor = isSelected ? R.color.accent_primary : R.color.text_primary; // 替换为你的选中颜色
        int textColor = isSelected ? R.color.accent_primary : R.color.text_primary;
        imageView.setColorFilter(ContextCompat.getColor(a, tintColor));
        textView.setTextColor(ContextCompat.getColor(a, textColor));
    }

    // 重置翻页样式选项的选中状态
    private void resetPageTurnSelection() {
        setFooterSelectStyle(pageTurnFlipImage, pageTurnFlipText, false);
        setFooterSelectStyle(pageTurnSlideImage, pageTurnSlideText, false);
        setFooterSelectStyle(pageTurnScrollImage, pageTurnScrollText, false);
    }

    private synchronized void updateFontSize(int size) {
        BookCSS.get().fontSizeSp = size;
        fontSizeValue.setText(size + "");
    }

    private synchronized void updateFontWeight(int size) {
        BookCSS.get().fontWeight = size;
        textEnhancementValue.setText(size + "");
    }


    private void resetAllFooterStates(Context context) {
        // 隐藏所有内容布局
//        catalogLayout.setVisibility(View.GONE);
        progressLayout.setVisibility(View.GONE);
        styleLayout.setVisibility(View.GONE);
        progressSeekBar.setProgress(dc.getCurentPage());
        progressSeekBar.setMax(dc.getPageCount());
        catalogCurProgress.setText(progressSeekBar.getProgress() + "/" + progressSeekBar.getMax());
        // 重置所有按钮状态
        footerCatalogImage.setColorFilter(ContextCompat.getColor(context, R.color.text_primary));
        footerProgressImage.setColorFilter(ContextCompat.getColor(context, R.color.text_primary));
        footerStyleImage.setColorFilter(ContextCompat.getColor(context, R.color.text_primary));
        footerReflowImage.setColorFilter(ContextCompat.getColor(context, R.color.text_primary));

        footerCatalogText.setTextColor(ContextCompat.getColor(context, R.color.text_primary)); // 默认文本颜色
        footerProgressText.setTextColor(ContextCompat.getColor(context, R.color.text_primary));
        footerStyleText.setTextColor(ContextCompat.getColor(context, R.color.text_primary));
        footerReflowText.setTextColor(ContextCompat.getColor(context, R.color.text_primary));
    }

    private void selectCatalog(Context context) {
        resetAllFooterStates(context);
        currentLayout = 1;
        // 设置选中状态
        footerCatalogImage.setColorFilter(ContextCompat.getColor(context, R.color.accent_primary)); // 选中状态的图标
        footerCatalogText.setTextColor(ContextCompat.getColor(context, R.color.accent_primary));
        // 显示对应内容
//        catalogLayout.setVisibility(View.VISIBLE);
    }

    private void selectProgress(Context context) {
        if (currentLayout == 2 && progressLayout.getVisibility() == View.VISIBLE) {
            progressLayout.setVisibility(View.GONE);
            return;
        }
        resetAllFooterStates(context);
        currentLayout = 2;
        // 设置选中状态
        footerProgressImage.setColorFilter(ContextCompat.getColor(context, R.color.accent_primary));
        footerProgressText.setTextColor(ContextCompat.getColor(context, R.color.accent_primary));
        // 显示对应内容
        progressLayout.setVisibility(View.VISIBLE);
    }

    private void selectStyle(Context context) {
        if (currentLayout == 3 && styleLayout.getVisibility() == View.VISIBLE) {
            styleLayout.setVisibility(View.GONE);
            return;
        }

        resetAllFooterStates(context);
        currentLayout = 3;
        // 设置选中状态
        footerStyleImage.setColorFilter(ContextCompat.getColor(context, R.color.accent_primary));
        footerStyleText.setTextColor(ContextCompat.getColor(context, R.color.accent_primary));

        // 显示对应内容
        styleLayout.setVisibility(View.VISIBLE);
    }

    private void selectReflow(Context context) {
        resetAllFooterStates(context);
        currentLayout = 4;
        // 设置选中状态
        footerReflowImage.setColorFilter(ContextCompat.getColor(context, R.color.accent_primary));
        footerReflowText.setTextColor(ContextCompat.getColor(context, R.color.accent_primary));

        // 显示对应内容 - 这里需要根据你的reflow布局实现
        // reflowLayout.setVisibility(View.VISIBLE);

        Toast.makeText(context, "Reflow功能尚未实现", Toast.LENGTH_SHORT).show();
    }

    public void updateSeekBarColorAndSize() {
//        lirbiLogo.setText(AppState.get().musicText);
        // TintUtil.setBackgroundFillColorBottomRight(ttsActive,
        // ColorUtils.setAlphaComponent(TintUtil.color, 230));

        int textColor = MagicHelper.getTextOrIconColor();

//        TintUtil.setTintImageWithAlpha(textToSpeachTop, textColor);
//        TintUtil.setTintImageWithAlpha(lockUnlockTop, textColor);

        //int titleColor = AppState.get().isDayNotInvert ? MagicHelper.otherColor(AppState.get().colorDayBg, -0.05f) : MagicHelper.otherColor(AppState.get().colorNigthBg, 0.05f);
        titleBar.setBackgroundColor(Color.TRANSPARENT);

//        int progressColor = TintUtil.color;

        // textSize
//        bookName.setTextSize(AppState.get().statusBarTextSizeAdv);
//        int iconSize = Dips.spToPx(AppState.get().statusBarTextSizeAdv);


//        textToSpeachTop.getLayoutParams().height = textToSpeachTop.getLayoutParams().width = iconSize;
//        lockUnlockTop.getLayoutParams().height = lockUnlockTop.getLayoutParams().width = iconSize;

    }

    @Subscribe
    public void onMessegeBrightness(MessegeBrightness msg) {
        BrightnessHelper.onMessegeBrightness(handler, msg, toastBrightnessText, overlay);
    }

    public View.OnClickListener onAutoScroll = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            onAutoScrollClick();
        }
    };

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
    public void tintSpeed() {
        try {
            int tintColor = MagicHelper.getTintColor();
//            speedSeekBar.getProgressDrawable().mutate().setColorFilter(tintColor,
//                    PorterDuff.Mode.SRC_IN);
//
//            speedSeekBar.getThumb().setColorFilter(tintColor,
//                    PorterDuff.Mode.SRC_IN);

            int color = MagicHelper.getTextOrIconColor();

            seekBar.getProgressDrawable().mutate().setColorFilter(color,
                    PorterDuff.Mode.SRC_IN);

            seekBar.getThumb().setColorFilter(color,
                    PorterDuff.Mode.SRC_IN);

            //line1.getBackground().
            TintUtil.setStrokeColorWithDash(line1, tintColor);
            TintUtil.setStrokeColorWithDash(line2, tintColor);

        } catch (Exception e) {
            LOG.e(e);
        }

    }

    public void showEditDialogIfNeed() {
        DragingDialogs.dialogEditColors(anchor, dc, drawView, true);
    }

    public void doDoubleTap(int x, int y) {
        if (dc.isMusicianMode()) {
            if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_ADJUST_PAGE) {
                dc.alignDocument();
            }
        } else {
            if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_ZOOM_IN_OUT) {
                dc.onZoomInOut(x, y);
                AppState.get().isEditMode = false;
                hideShow();
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_ADJUST_PAGE) {
                dc.alignDocument();
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CENTER_HORIZONTAL) {
                dc.centerHorizontal();
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_AUTOSCROLL) {
                onAutoScrollClick();
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CLOSE_BOOK) {
                closeAndRunList();
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CLOSE_HIDE_APP) {
                Apps.showDesctop(a);
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_SHARE_PAGE) {
                ExtUtils.sharePage(dc, dc.getCurentPage() - 1);
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_START_STOP_TTS) {
//                TTSService.playPause(dc.getActivity(), dc);
            } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CLOSE_BOOK_AND_APP) {
                dc.onCloseActivityFinal(new Runnable() {
                    @Override
                    public void run() {
                        MainTabs2.closeApp(dc.getActivity());
                    }
                });
                dc.closeActivity();
            }
        }
    }


    public void doShowHideWrapperControlls() {
        AppState.get().isEditMode = !AppState.get().isEditMode;
        hideShow();
        Keyboards.invalidateEink(parentParent);

    }

    public void showHideHavigationBar() {
        if (!AppState.get().isEditMode && AppState.get().fullScreenMode == AppState.FULL_SCREEN_FULLSCREEN) {
            Keyboards.hideNavigation(a);
        }
    }

    public void doChooseNextType(View view) {
        final MyPopupMenu popupMenu = new MyPopupMenu(view.getContext(), view);

        String pages = dc.getString(R.string.by_pages);
        String screen = dc.getString(R.string.of_screen).toLowerCase(Locale.US);
        String screens = dc.getString(R.string.by_screans);
        final List<Integer> values = Arrays.asList(AppState.NEXT_SCREEN_SCROLL_BY_PAGES, 100, 95, 75, 50, 25, 10);

        for (int i = 0; i < values.size(); i++) {
            final int n = i;
            String name = i == AppState.NEXT_SCREEN_SCROLL_BY_PAGES ? pages : values.get(i) + "% " + screen;
            if (values.get(i) == 100) {
                name = screens;
            }

            popupMenu.getMenu().add(name).setOnMenuItemClickListener(new OnMenuItemClickListener() {

                @Override
                public boolean onMenuItemClick(MenuItem item) {
                    AppState.get().nextScreenScrollBy = values.get(n);
                    initNextType();
                    Keyboards.hideNavigation(dc.getActivity());
                    return false;
                }
            });
        }

        popupMenu.getMenu().add(R.string.custom_value).setOnMenuItemClickListener(new OnMenuItemClickListener() {

            @Override
            public boolean onMenuItemClick(MenuItem item) {
                Activity a = dc.getActivity();
                final AlertDialog.Builder builder = new AlertDialog.Builder(a);
                builder.setTitle(R.string.custom_value);

                final CustomSeek myValue = new CustomSeek(a);
                myValue.init(1, 100, AppState.get().nextScreenScrollMyValue);
                myValue.setOnSeekChanged(new IntegerResponse() {

                    @Override
                    public boolean onResultRecive(int result) {
                        AppState.get().nextScreenScrollMyValue = result;
                        myValue.setValueText(AppState.get().nextScreenScrollMyValue + "%");
                        return false;
                    }
                });
                myValue.setValueText(AppState.get().nextScreenScrollMyValue + "%");

                builder.setView(myValue);

                builder.setPositiveButton(R.string.apply, new OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        AppState.get().nextScreenScrollBy = AppState.get().nextScreenScrollMyValue;
                        initNextType();
                        Keyboards.hideNavigation(dc.getActivity());

                    }
                });
                builder.setNegativeButton(R.string.cancel, new OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                    }
                });

                builder.show();

                return false;
            }

        });

        popupMenu.show();

    }

    public View.OnClickListener onBookmarks = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            DragingDialogs.dialogShowBookmarks(anchor, dc, new Runnable() {

                @Override
                public void run() {
                    showHideHistory();
                    showPagesHelper();
                    updateUI();
                }
            });
        }
    };

    public void doHideShowToolBar() {
        AppState.get().isShowToolBar = !AppState.get().isShowToolBar;
        initToolBarPlusMinus();
    }

    public void initToolBarPlusMinus() {
        if (AppState.get().isEditMode || AppState.get().isShowToolBar) {
            titleBar.setVisibility(View.VISIBLE);
        } else {
            titleBar.setVisibility(View.GONE);
        }
    }

    public void initNextType() {
    }

    public OnLongClickListener onBookmarksLong = new OnLongClickListener() {

        @Override
        public boolean onLongClick(final View arg0) {
            DragingDialogs.dialogAddBookmarksLong(anchor, dc);
            showPagesHelper();
            return true;
        }
    };

    public void hideShow() {
//        if (AppState.get().isEnableAccessibility) {
//            //AppState.get().isEditMode = true;
//        }

        if (AppState.get().isEditMode) {
            DocumentController.turnOnButtons(a);
            show();
        } else {
            DocumentController.turnOffButtons(a);
            hide();
        }

        initToolBarPlusMinus();

        if (AppState.get().isAutoScroll) {
//            autoScroll.setImageResource(R.drawable.glyphicons_174_pause);
        } else {
//            autoScroll.setImageResource(R.drawable.glyphicons_439_video_play_empty);
        }


        // hideSeekBarInReadMode();
        // showHideHavigationBar();
        DocumentController.chooseFullScreen(dc.getActivity(), AppState.get().fullScreenMode);
        showPagesHelper();
        //try eink fix
    }

    public void hide() {
        overlay2.setVisibility(View.GONE);
        menuLayout.setVisibility(View.GONE);
        bottomBar.setVisibility(View.GONE);
    }


    public View.OnClickListener onMenu = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            LOG.d("DEBUG", "Click");
            doShowHideWrapperControlls();
        }
    };

    public void show() {
        titleBar.setVisibility(View.VISIBLE);
        overlay2.setVisibility(View.VISIBLE);
        menuLayout.setVisibility(View.VISIBLE);
        bottomBar.setVisibility(View.VISIBLE);
        if (currentLayout == 1) {
            selectCatalog(a);
        } else if (currentLayout == 2) {
            selectProgress(a);
        } else if (currentLayout == 3) {
            selectStyle(a);
        } else if (currentLayout == 4) {
            selectReflow(a);
        }
        updateLock();
    }

    public void showSearchDialog() {
        if (AppSP.get().isCut) {
            onModeChange.setImageResource(R.drawable.my_glyphicons_two_page_one);
            AppSP.get().isCut = !false;
            onCut.onClick(null);
        }
        if (AppSP.get().isCrop) {
            onCrop.onClick(null);
        }

        DragingDialogs.dialogSearchText(anchor, dc, "");
    }

    public void onAutoScrollClick() {
        if (dc.isVisibleDialog()) {
            return;
        }

        AppState.get().isAutoScroll = !AppState.get().isAutoScroll;
        // changeAutoScrollButton();
        dc.onAutoScroll();
        updateUI();
    }

//    public View.OnClickListener onReverseKeys = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            LOG.d("DEBUG", "Click");
//            AppState.get().isReverseKeys = !AppState.get().isReverseKeys;
//            updateUI();
//        }
//    };

    private boolean closeDialogs() {
        return dc.closeDialogs();
    }

    public void hideAds() {
        // adFrame.setTag("");
        //  adFrame.setVisibility(View.GONE);
    }

    public void nextChose(boolean animate) {
        nextChose(animate, 0);
    }

//    public View.OnClickListener onFull = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View v) {
//            DocumentController.showFullScreenPopup(dc.getActivity(), v, id -> {
//                AppState.get().fullScreenMode = id;
//                fullscreen.setImageResource(DocumentController.getFullScreenIcon(a, AppState.get().fullScreenMode));
//
//                if (dc.isTextFormat()) {
//                    onRefresh.run();
//                    dc.restartActivity();
//                }
//                DocumentController.chooseFullScreen(a, AppState.get().fullScreenMode);
//                return true;
//            }, AppState.get().fullScreenMode);
//        }
//    };

    public void nextChose(boolean animate, int repeatCount) {
        LOG.d("nextChose");
        dc.checkReadingTimer();

        if (AppState.get().isEditMode) {
            AppState.get().isEditMode = false;
        }

        if (AppState.get().nextScreenScrollBy == AppState.NEXT_SCREEN_SCROLL_BY_PAGES) {
            dc.onNextPage(animate);
        } else {
            if (AppState.get().nextScreenScrollBy <= 50 && repeatCount == 0) {
                animate = true;
            }
            dc.onNextScreen(animate);
        }
        checkBookmarkStatus(dc.getCurentPage());
        //updateUI();

    }

    public void prevChose(boolean animate) {
        prevChose(animate, 0);
    }

    public void prevChose(boolean animate, int repeatCount) {
//        dc.checkReadingTimer();
        if (AppState.get().isEditMode) {
            AppState.get().isEditMode = false;
        }

        if (AppState.get().nextScreenScrollBy == AppState.NEXT_SCREEN_SCROLL_BY_PAGES) {
            dc.onPrevPage(animate);
        } else {
            if (AppState.get().nextScreenScrollBy <= 50 && repeatCount == 0) {
                animate = true;
            }
            dc.onPrevScreen(animate);
        }
        checkBookmarkStatus(dc.getCurentPage());
        //updateUI();
    }

    public void checkBookmarkStatus(int page) {
        // 检测页面标签
        if (hasCurrentPageBookmark(page)) {
            bookmarkBtn.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark_selected));
            bookmarkBtn.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
            bookmark2.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark_selected));
            bookmark2.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        } else {
            bookmarkBtn.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark));
            bookmarkBtn.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
            bookmark2.setImageDrawable(ContextCompat.getDrawable(a, R.drawable.ic_bookmark));
            bookmark2.setColorFilter(ContextCompat.getColor(a, R.color.text_primary));
        }
    }

    public View.OnClickListener onCrop = new View.OnClickListener() {

        @Override
        public void onClick(final View v) {
            DragingDialogs.dialogCustomCrop(anchor, dc, new Runnable() {

                @Override
                public void run() {
                    dc.onCrop();
                    updateUI();

                    AppState.get().isEditMode = false;
                    hideShow();
                    hideShowEditIcon();
                }
            });
        }
    };

    public void setTitle(final String title) {
        this.bookTitle = title;

        hideShowEditIcon();

    }

    public void hideShowEditIcon() {
        if (dc != null && !BookType.PDF.is(dc.getCurrentBook().getPath())) {
//            editTop2.setVisibility(View.GONE);
        } else if (AppSP.get().isCrop || AppSP.get().isCut) {
//            editTop2.setVisibility(View.GONE);
        } else {
            boolean passwordProtected = dc.isPasswordProtected();
            LOG.d("passwordProtected", passwordProtected);
            if (dc != null && passwordProtected) {
//                editTop2.setVisibility(View.GONE);
            } else {
                if (AppsConfig.MUPDF_FZ_VERSION.equals(AppsConfig.MUPDF_1_11)) {
//                    editTop2.setVisibility(View.VISIBLE);
                } else {
//                    editTop2.setVisibility(View.VISIBLE);
                }
            }
        }

    }

    public DocumentController getController() {
        return dc;
    }

    public OnLongClickListener onCropLong = new OnLongClickListener() {

        @Override
        public boolean onLongClick(View v) {
            AppSP.get().isCrop = !AppSP.get().isCrop;

            dc.onCrop();
            updateUI();

            AppState.get().isEditMode = false;
            hideShow();
            hideShowEditIcon();
            return true;
        }
    };

    public void showHelp() {
        if (AppSP.get().isFirstTimeVertical) {
            handler.postDelayed(new Runnable() {

                @Override
                public void run() {
                    AppSP.get().isFirstTimeVertical = false;
                    AppState.get().isEditMode = true;
                    hideShow();
//                    Views.showHelpToast(lockUnlock);

                }
            }, 1000);
        }
    }

    public void showPagesHelper() {
//        try {
////            BookmarkPanel.showPagesHelper(pageshelper, musicButtonPanel, dc, pagesBookmark, quickBookmark, onRefresh);
//        } catch (Exception e) {
//            LOG.e(e);
//        }
    }

    public View.OnClickListener onCut = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            AppSP.get().isCrop = false; // no crop with cut
            AppState.get().cutP = 50;
            AppSP.get().isCut = !AppSP.get().isCut;

//            crop.setVisibility(AppSP.get().isCut ? View.GONE : View.VISIBLE);


            dc.onCrop();// crop false
            dc.updateRendering();
            dc.alignDocument();

            updateUI();
            titleBar.setOnTouchListener(new HorizontallSeekTouchEventListener(onSeek, dc.getPageCount(), false));
        }
    };

    public void showOutline(final List<OutlineLinkWrapper> list, final int count) {
        try {
            dc.activity.runOnUiThread(new Runnable() {

                @Override
                public void run() {
                    onCloseBook.setVisibility(View.VISIBLE);
                    pagesCountIndicator.setVisibility(View.VISIBLE);

                    showHelp();

                    hideShowEditIcon();

                    updateSpeedLabel();

                    DialogsPlaylist.dispalyPlaylist(a, dc);
                    HypenPanelHelper.init(parentParent, dc);


                    showPagesHelper();

                }
            });
        } catch (Exception e) {
            LOG.e(e);
        }

    }

    public void onResume() {
        LOG.d("DocumentWrapperUI", "onResume");
        handlerTimer.post(updateTimePower);

        if (dc != null) {
            dc.goToPageByTTS();
        }

//        if (ttsActive != null) {
//            ttsActive.setVisibility(TxtUtils.visibleIf(TTSEngine.get().isTempPausing()));
//        }

    }

    public void onPause() {
        LOG.d("DocumentWrapperUI", "onPause");
        handlerTimer.removeCallbacks(updateTimePower);
        dc.resetReadTimer();
    }

    public View.OnClickListener onPrefTop = new View.OnClickListener() {

        @Override
        public void onClick(final View arg0) {
            DragingDialogs.dialogPreferences(anchor, dc, onRefresh, new Runnable() {

                @Override
                public void run() {
                    updateUI();
                }
            });
        }
    };

    public void onDestroy() {
        LOG.d("DocumentWrapperUI", "onDestroy");
        handlerTimer.removeCallbacksAndMessages(null);
        handler.removeCallbacksAndMessages(null);

    }

    public void onConfigChanged() {
        try {
            updateUI();
        } catch (Exception e) {
            LOG.e(e);
        }
    }

    public void onLoadBookFinish() {
        onCloseBook.setVisibility(View.VISIBLE);
    }

    Runnable updateUIRunnable = new Runnable() {

        @Override
        public void run() {
            updateUI();
        }
    };


//    View.OnClickListener onItemMenu = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View v) {
//            ShareDialog.show(a, dc.getCurrentBook(), new Runnable() {
//
//                @Override
//                public void run() {
//                    if (dc.getCurrentBook().delete()) {
//                        TempHolder.listHash++;
//                        AppDB.get().deleteBy(dc.getCurrentBook().getPath());
//                        dc.getActivity().finish();
//                    }
//                }
//            }, dc.getCurentPage() - 1, dc, new Runnable() {
//
//                @Override
//                public void run() {
//                    hideShow();
//
//                }
//            });
//            Keyboards.hideNavigation(a);
//            hideAds();
//        }
//    };


//    View.OnClickListener onLirbiLogoClick = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View v) {
//            doShowHideWrapperControlls();
//        }
//    };


    View.OnClickListener onGoToPAge1 = new View.OnClickListener() {

        @Override
        public void onClick(View v) {
            dc.onScrollY(0);
            updateUI();
        }
    };


//    public View.OnClickListener onNormalMode = new View.OnClickListener() {
//
//        @Override
//        public void onClick(final View arg0) {
//            AppSP.get().readingMode = AppState.READING_MODE_BOOK;
//            initUI(a);
//            hideShow();
//        }
//    };


    // public void changeAutoScrollButton() {
    // if (AppState.get().isAutoScroll) {
    // autoScroll.setImageResource(android.R.drawable.ic_media_pause);
    // seekSpeedLayot.setVisibility(View.VISIBLE);
    // } else {
    // autoScroll.setImageResource(android.R.drawable.ic_media_play);
    // seekSpeedLayot.setVisibility(View.GONE);
    // }
    //
    // }


}
