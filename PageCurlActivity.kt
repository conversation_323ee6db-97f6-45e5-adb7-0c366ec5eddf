package com.tct.exbook.pdf.search.activity

import android.app.AlertDialog
import android.content.DialogInterface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.InputType
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.Toast
import com.tct.exbook.R
import com.tct.exbook.android.utils.LOG
import com.tct.exbook.android.utils.TxtUtils
import com.tct.exbook.ext.CacheZipUtils
import com.tct.exbook.model.AppSP
import com.tct.exbook.model.AppState
import com.tct.exbook.pdf.CopyAsyncTask
import com.tct.exbook.pdf.info.ExtUtils
import com.tct.exbook.pdf.info.PasswordDialog
import com.tct.exbook.pdf.info.model.BookCSS
import com.tct.exbook.pdf.info.view.BrightnessHelper
import com.tct.exbook.pdf.info.view.Dialogs
import com.tct.exbook.pdf.info.wrapper.DocumentController
import com.tct.exbook.pdf.info.wrapper.PageCurlWrapperUI
import com.tct.exbook.pdf.search.activity.msg.InvalidateMessage
import com.tct.exbook.pdf.search.view.pagecurl.PageCurlView
import com.tct.exbook.sys.TempHolder
import com.tct.exbook.ui2.AdsFragmentActivity
import com.tct.exbook.ui2.FileMetaCore
import org.ebookdroid.common.settings.SettingsManager
import org.ebookdroid.droids.mupdf.codec.exceptions.MuPdfPasswordException
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.Executors


public class PageCurlActivity :
    AdsFragmentActivity() {

    private var handler: Handler? = null
    private lateinit var mCurlView: PageCurlView

    var loadinAsyncTask: CopyAsyncTask<*, *, *>? = null

    var pageCurlWrapperUI: PageCurlWrapperUI? = null
    var dc: HorizontalModeController? = null
    override fun onFinishActivity() {
//        getController().closeActivityFinal(null)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        DocumentController.doRotation(this)
        DocumentController.doContextMenu(this)
        FileMetaCore.checkOrCreateMetaInfo(this)

        if (getIntent().getData() != null) {
            val path = getIntent().getData()!!.getPath()
            val bs = SettingsManager.getBookSettings(path)
            // AppState.get().setNextScreen(bs.isNextScreen);
            if (bs != null) {
                // AppState.get().l = bs.l;
                AppState.get().autoScrollSpeed = bs.s
                val isTextFormat = ExtUtils.isTextFomat(bs.path)
                AppSP.get().isCut = if (isTextFormat) false else bs.sp //important!!!
                AppSP.get().isCrop = bs.cp
                AppSP.get().isDouble = false
                AppSP.get().isDoubleCoverAlone = false
                AppSP.get().isLocked = bs.getLock(isTextFormat)
                TempHolder.get().pageDelta = bs.d
                if (AppState.get().isCropPDF && !AppSP.get().isCut && !isTextFormat) {
                    AppSP.get().isCrop = true
                }
            }

            BookCSS.get().detectLang(path)
        }

        BrightnessHelper.applyBrigtness(this)

        if (AppState.get().isDayNotInvert) {
            setTheme(R.style.StyledIndicatorsWhite)
        } else {
            setTheme(R.style.StyledIndicatorsBlack)
        }

        super.onCreate(savedInstanceState)
        //FirebaseAnalytics.getInstance(this);
        if (PasswordDialog.isNeedPasswordDialog(this)) {
            return
        }
        setContentView(R.layout.activity_vertical_view)
        DocumentController.applyEdgeToEdge(this)

        val frameLayout = findViewById<View?>(R.id.documentView) as FrameLayout

        handler = Handler(Looper.getMainLooper())
        loadinAsyncTask = object : CopyAsyncTask<Any?, Any?, Any?>() {
            var dialog: AlertDialog? = null
            var start: Long = 0
            private var isCancelled = false

            override fun onPreExecute() {
                start = System.currentTimeMillis()
                TempHolder.get().loadingCancelled = false
                dialog = Dialogs.loadingBook(this@PageCurlActivity, object : Runnable {
                    override fun run() {
                        isCancelled = true
                        TempHolder.get().loadingCancelled = true
                        CacheZipUtils.removeFiles(CacheZipUtils.CACHE_BOOK_DIR.listFiles())
                        finish()
                    }
                })
            }


            override fun doInBackground(vararg params: Any?): Any {
                try {
                    LOG.d(
                        "doRotation(this)",
                        AppState.get().orientation,
                        <EMAIL>
                    )
                    try {
                        //Thread.sleep(3000);
                        <EMAIL>(object : Runnable {
                            override fun run() {
                                dc = object : HorizontalModeController(
                                    this@PageCurlActivity,
                                    frameLayout.width,
                                    frameLayout.height
                                ) {
                                    override fun onGoToPageImpl(page: Int) {
                                        pageCurlWrapperUI?.updateUI()
                                        EventBus.getDefault().post(InvalidateMessage())
                                    }

                                    override fun notifyAdapterDataChanged() {
                                    }

                                    override fun showInterstialAndClose() {
                                    }
                                }
                                mCurlView = PageCurlView(base = dc!!, this@PageCurlActivity)
                                frameLayout.addView(mCurlView)
                                pageCurlWrapperUI = PageCurlWrapperUI(dc)
                                pageCurlWrapperUI?.initUI(this@PageCurlActivity)
//                                updateBannnerTop()
                            }
                        })
                    } catch (e: InterruptedException) {
                    }

                } catch (e: MuPdfPasswordException) {
                    return -1
                } catch (e: RuntimeException) {
                    LOG.e(e)
                    return -2
                }
                return 0
            }

            override fun onCancelled() {
                try {
                    if (dialog != null) {
                        dialog!!.dismiss()
                    }
                } catch (e: Exception) {
                }
                isCancelled = true
            }

            override fun onPostExecute(result: Any?) {
                try {
                    // onClose.setVisibility(View.VISIBLE);
                    LOG.d("RESULT", result)
                    if (dialog != null) {
                        dialog!!.dismiss()
                    }
                } catch (e: Exception) {
                }
                if (isCancelled) {
                    LOG.d("Cancelled")
                    finish()
                    return
                }
                if (result as Int? == -2) {
                    Toast.makeText(
                        this@PageCurlActivity,
                        R.string.msg_unexpected_error,
                        Toast.LENGTH_SHORT
                    ).show()
                    AppState.get().isEditMode = true
//                    hideShow()
//                    onClose.setVisibility(View.VISIBLE)
                    return
                }

                if (result as Int == -1) {
                    val input = EditText(this@PageCurlActivity)
                    input.setSingleLine(true)
                    input.setInputType(InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD)

                    val dialog = AlertDialog.Builder(this@PageCurlActivity)
                    dialog.setTitle(R.string.enter_password)
                    dialog.setView(input)
                    dialog.setCancelable(false)
                    dialog.setNegativeButton(
                        R.string.cancel,
                        object : DialogInterface.OnClickListener {
                            override fun onClick(dialog: DialogInterface, which: Int) {
                                dialog.dismiss()
                                if (dc != null) {
                                    dc?.onCloseActivityFinal(null)
                                } else {
                                    <EMAIL>()
                                }
                            }
                        })
                    dialog.setPositiveButton(
                        R.string.open_file,
                        object : DialogInterface.OnClickListener {
                            override fun onClick(dialog: DialogInterface, which: Int) {
                                val txt = input.getText().toString()
                                if (TxtUtils.isNotEmpty(txt)) {
                                    dialog.dismiss()

                                    val runnable = Runnable {
                                        <EMAIL>()
                                        getIntent().putExtra(
                                            HorizontalModeController.EXTRA_PASSWORD,
                                            txt
                                        )
                                        startActivity(getIntent())
                                    }
                                    if (dc != null) {
                                        dc?.onCloseActivityFinal(runnable)
                                    } else {
                                        runnable.run()
                                    }
                                } else {
                                    if (dc == null) {
                                        <EMAIL>()
                                    } else {
                                        dc?.onCloseActivityFinal(null)
                                    }
                                }
                            }
                        })
                    dialog.show()
                } else {
                    val s=dc?.getPageUrl(0)?.path
                    LOG.d("测试RESULT", s)
                    mCurlView.setInitView(0)
                }
            }
        }
        loadinAsyncTask!!.executeOnExecutor(Executors.newSingleThreadExecutor())
//        getController().onBookLoaded(object : Runnable {
//            override fun run() {
//                init(mCurlView.width, mCurlView.height)
//            }
//        })
    }

    private fun init(w: Int, h: Int) {
        mCurlView.setInitView(0)
    }

    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
    }

    override fun onBackPressedImpl() {
        TODO("Not yet implemented")
    }

    override fun onBackPressedFinishImpl() {
        TODO("Not yet implemented")
    }
}