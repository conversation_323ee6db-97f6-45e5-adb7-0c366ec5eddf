package com.tct.exbook.pdf.search.view.pagecurl

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextPaint
import android.util.AttributeSet
import android.util.Log
import android.util.Pair
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.Scroller
import android.widget.Toast
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.rtfparserkit.rtf.Command
import com.tct.exbook.AdamApp
import com.tct.exbook.android.utils.Apps
import com.tct.exbook.android.utils.Dips
import com.tct.exbook.android.utils.LOG
import com.tct.exbook.android.utils.TxtUtils
import com.tct.exbook.android.utils.Vibro
import com.tct.exbook.model.AppSP
import com.tct.exbook.model.AppState
import com.tct.exbook.pdf.info.IMG
import com.tct.exbook.R
import com.tct.exbook.pdf.info.model.BookCSS
import com.tct.exbook.pdf.info.view.BrightnessHelper
import com.tct.exbook.pdf.info.view.Dialogs
import com.tct.exbook.pdf.info.wrapper.DocumentController
import com.tct.exbook.pdf.info.wrapper.MagicHelper
import com.tct.exbook.pdf.search.activity.HorizontalModeController
import com.tct.exbook.pdf.search.activity.PageImageState
import com.tct.exbook.pdf.search.activity.PageImaveView
import com.tct.exbook.pdf.search.activity.SimpleTouchOnGestureListener
import com.tct.exbook.pdf.search.activity.msg.InvalidateMessage
import com.tct.exbook.pdf.search.activity.msg.MessageAutoFit
import com.tct.exbook.pdf.search.activity.msg.MessageCenterHorizontally
import com.tct.exbook.pdf.search.activity.msg.MessageEvent
import com.tct.exbook.pdf.search.activity.msg.MessagePageXY
import com.tct.exbook.sys.ClickUtils
import com.tct.exbook.sys.TempHolder
import org.ebookdroid.core.ViewState
import org.ebookdroid.core.codec.Annotation
import org.ebookdroid.core.codec.PageLink
import org.ebookdroid.droids.mupdf.codec.TextWord
import org.ebookdroid.ui.viewer.IActivityController
import org.ebookdroid.ui.viewer.IView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.lang.ref.WeakReference
import kotlin.concurrent.Volatile
import kotlin.math.abs
import kotlin.math.acos
import kotlin.math.atan
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.math.tan

class PageCurlView : View{

    var MIN: Int = Dips.dpToPx(15)

    @Volatile
    var isFirstZoomInOut: Boolean = true

    @Volatile
    var prevLock: Boolean = false
    var rect: Paint = Paint().apply {
        color = Color.DKGRAY;
        strokeWidth = Dips.dpToPx(1).toFloat()
        style = Paint.Style.STROKE;
    }
    var gestureDetector: GestureDetector? = null
    @get:JvmName("getScrooler2")
    var scroller: Scroller? = null
    var imageGestureListener: ImageSimpleGestureListener? = null
    var paintWrods: Paint = Paint()
    var downX: Float = 0f
    var downY: Float = 0f
    var xInit: Float = 0f
    var yInit: Float = 0f
    var cx: Float = 0f
    var cy: Float = 0f
    var distance: Float = 0f
    var clickUtils: ClickUtils? = null
    var brightnessHelper: BrightnessHelper? = null
    var dp1: Int = Dips.dpToPx(1)

    private var isReadyForMove = false
    private var isLognPress = false
    private var isIgronerClick = false

    private var isMoveNextPrev = 0

    var drawableHeight: Int = 0
    var drawableWidth: Int = 0

    private var mTextPaint: Paint? = null
    private var mTextPaintShadow: TextPaint? = null

    /**
     * Px / Draw call
     */
    private var mCurlSpeed = 0

    /**
     * Fixed update time used to create a smooth curl animation
     */
    private var mUpdateRate = 0

    /**
     * The initial offset for x and y axis movements
     */
    private var mInitialEdgeOffset = 0

    /**
     * The mode we will use
     */
    private var mCurlMode = 0

    /**
     * Enable/Disable debug mode
     */
    private var bEnableDebugMode = false

    /**
     * The context which owns us
     */
    private var mContext: WeakReference<Context?>? = null

    /**
     * Handler used to auto flip time based
     */
    private var mAnimationHandler: FlipAnimationHandler? = null

    /**
     * Maximum radius a page can be flipped, by default it's the width of the view
     */
    private var mFlipRadius = 0f

    /**
     * Point used to move
     */
    private var mMovement: Vector2D? = null

    /**
     * The finger position
     */
    private var mFinger: Vector2D? = null

    /**
     * Movement point form the last frame
     */
    private var mOldMovement: Vector2D? = null

    /**
     * Page curl edge
     */
    private var mCurlEdgePaint: Paint? = null

    /**
     * Our points used to define the current clipping paths in our draw call
     */
    private var mA: Vector2D? = null
    private var mB: Vector2D? = null
    private var mC: Vector2D? = null
    private var mD: Vector2D? = null
    private var mE: Vector2D? = null
    private var mF: Vector2D? = null
    private var mOldF: Vector2D? = null
    private var mOrigin: Vector2D? = null

    /**
     * Left and top offset to be applied when drawing
     */
    private var mCurrentLeft = 0
    private var mCurrentTop = 0

    /**
     * If false no draw call has been done
     */
    private var bViewDrawn = false

    /**
     * Defines the flip direction that is currently considered
     */
    private var bFlipRight = false

    /**
     * If TRUE we are currently auto-flipping
     */
    private var bFlipping = false

    private var bShouldFlip = false // 新增类成员变量


    /**
     * TRUE if the user moves the pages
     */
    private var bUserMoves = false

    /**
     * Used to control touch input blocking
     */
    private var bBlockTouchInput = false

    /**
     * Enable input after the next draw event
     */
    private var bEnableInputAfterDraw = false

    /**
     * LAGACY The current foreground
     */
    private var mForeground: BitmapDrawable? = null

    /**
     * LAGACY The current background
     */
    private var mBackground: BitmapDrawable? = null

    /**
     * LAGACY List of pages, this is just temporal
     */
    private var pageCount = 0

    /**
     * LAGACY Current selected page
     */
    private var mIndex = 0


    private var base: HorizontalModeController? = null


    private var currentTarget: CustomTarget<Bitmap?>? = null

    private var handler: Handler? = null

    //设置点击区域
    var leftRect: RectF? = null
    var centerRect: RectF? = null
    var rightRect: RectF? = null


    var scrolling: Runnable = object : Runnable {
        override fun run() {
            if (scroller!!.isFinished()) {
                return
            }
            val more = scroller!!.computeScrollOffset()
            val xx = scroller!!.getCurrX()
            val yy = scroller!!.getCurrY()

            val dx = xx - downX
            val dy = yy - downY

            imageMatrix()!!.postTranslate(dx, dy)
            downY = yy.toFloat()
            downX = xx.toFloat()
            invalidate()

            if (more) {
                handler?.post(scrolling)
            }
        }
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 取消所有加载请求
        mBackground = null
        mForeground = null
        EventBus.getDefault().unregister(this)
    }


    /**
     * Inner class used to represent a 2D point.
     */
    private inner class Vector2D(var x: Float, var y: Float) {
        override fun toString(): String {
            // TODO Auto-generated method stub
            return "(" + this.x + "," + this.y + ")"
        }

        fun length(): Float {
            return sqrt((x * x + y * y).toDouble()).toFloat()
        }

        fun lengthSquared(): Float {
            return (x * x) + (y * y)
        }

        override fun equals(o: Any?): Boolean {
            if (o is Vector2D) {
                val p: Vector2D = o
                return p.x == x && p.y == y
            }
            return false
        }

        fun reverse(): Vector2D {
            return Vector2D(-x, -y)
        }

        fun sum(b: Vector2D): Vector2D {
            return Vector2D(x + b.x, y + b.y)
        }

        fun sub(b: Vector2D): Vector2D {
            return Vector2D(x - b.x, y - b.y)
        }

        fun dot(vec: Vector2D): Float {
            return (x * vec.x) + (y * vec.y)
        }

        fun cross(a: Vector2D, b: Vector2D): Float {
            return a.cross(b)
        }

        fun cross(vec: Vector2D): Float {
            return x * vec.y - y * vec.x
        }

        fun distanceSquared(other: Vector2D): Float {
            val dx = other.x - x
            val dy = other.y - y

            return (dx * dx) + (dy * dy)
        }

        fun distance(other: Vector2D): Float {
            return sqrt(distanceSquared(other).toDouble()).toFloat()
        }

        fun dotProduct(other: Vector2D): Float {
            return other.x * x + other.y * y
        }

        fun normalize(): Vector2D {
            val magnitude = sqrt(dotProduct(this).toDouble()).toFloat()
            return Vector2D(x / magnitude, y / magnitude)
        }

        fun mult(scalar: Float): Vector2D {
            return Vector2D(x * scalar, y * scalar)
        }
    }

    /**
     * Inner class used to make a fixed timed animation of the curl effect.
     */
    internal inner class FlipAnimationHandler : Handler() {
        override fun handleMessage(msg: Message) {
            <EMAIL>()
        }

        fun sleep(millis: Long) {
            this.removeMessages(0)
            sendMessageDelayed(obtainMessage(0), millis)
        }
    }

    /**
     * Base
     *
     * @param context
     */
    constructor(base: HorizontalModeController, context: Context?) : super(context) {
        this.base = base
        init(context)
        ResetClipEdge()
    }

    /**
     * Construct the object from an XML file. Valid Attributes:
     *
     * @see View.View
     */
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init(context)

        // Get the data from the XML AttributeSet

        // Get data
        bEnableDebugMode = false
        mCurlSpeed = 300
        mUpdateRate = 100
        mInitialEdgeOffset = 0
        mCurlMode = 1

        // recycle object (so it can be used by others)
        ResetClipEdge()
    }

    /**
     * Initialize the view
     */
    private fun init(context: Context?) {
        // Foreground text paint
        mTextPaint = Paint()
        mTextPaint!!.isAntiAlias = true
        mTextPaint!!.textSize = 16f
        mTextPaint!!.color = -0x1000000

        // The shadow
        mTextPaintShadow = TextPaint()
        mTextPaintShadow!!.isAntiAlias = true
        mTextPaintShadow!!.textSize = 16f
        mTextPaintShadow!!.color = 0x00000000

        // Cache the context
        mContext = WeakReference<Context?>(context)

        // Base padding
        setPadding(3, 3, 3, 3)

        // The focus flags are needed
        setFocusable(true)
        setFocusableInTouchMode(true)

        mMovement = Vector2D(0f, 0f)
        mFinger = Vector2D(0f, 0f)
        mOldMovement = Vector2D(0f, 0f)

        // Create our curl animation handler
        mAnimationHandler = FlipAnimationHandler()

        // Create our edge paint
        mCurlEdgePaint = Paint()
        mCurlEdgePaint!!.color = Color.WHITE
        mCurlEdgePaint!!.isAntiAlias = true
        mCurlEdgePaint!!.style = Paint.Style.FILL
        mCurlEdgePaint!!.setShadowLayer(10f, -5f, 5f, -0x67000000)

        // Set the default props, those come from an XML :D
        mCurlSpeed = 30
        mUpdateRate = 33
        mInitialEdgeOffset = 20
        mCurlMode = 0

        handler = Handler(Looper.getMainLooper())
        scroller = Scroller(getContext(), AccelerateDecelerateInterpolator())
        imageGestureListener = ImageSimpleGestureListener()
        gestureDetector = GestureDetector(context, imageGestureListener!!)
        paintWrods.strokeWidth = Dips.dpToPx(1).toFloat()
        paintWrods.textSize = 30f

        EventBus.getDefault().register(this)
        clickUtils = ClickUtils()
        brightnessHelper = BrightnessHelper(context)
        isSaveEnabled = false
        AppState.get().isLoaded = true
    }


    fun setInitView(index: Int) {
        this.mIndex = index
        this.pageCount=base?.pageCount?:0
        LOG.d("测试", "加载page ${this.pageCount}")
        Log.d("测试", "加载page anim")
        loadPage(index, {
            mForeground = it
            resetClickRect(width, height)
            autoFit()
            invalidate()
        })
        if (index + 1 < pageCount) {
            loadPage(index + 1, {
                mBackground = it
                autoFit()
                invalidate()
            })
        }
    }


    fun resetClickRect(viewWidth: Int, viewHeight: Int) {
        val areaWidth = viewWidth / 3f // 将宽度分成三等分
        leftRect = RectF(0f, 0f, areaWidth, viewHeight.toFloat())
        centerRect = RectF(areaWidth, 0f, areaWidth * 2, viewHeight.toFloat())
        rightRect = RectF(areaWidth * 2, 0f, viewWidth.toFloat(), viewHeight.toFloat())
    }

    fun createBitmapDrawable(bitmap: Bitmap): BitmapDrawable {
        val imageDrawable = BitmapDrawable(resources, bitmap)
        val drawableHeight = bitmap.height
        val drawableWidth = bitmap.width
        imageDrawable.setBounds(0, 0, drawableWidth, drawableHeight)
        return imageDrawable
    }

    /**
     * Reset points to it's initial clip edge state
     */
    fun ResetClipEdge() {
        // Set our base movement
        mMovement!!.x = mInitialEdgeOffset.toFloat()
        mMovement!!.y = mInitialEdgeOffset.toFloat()
        mOldMovement!!.x = 0f
        mOldMovement!!.y = 0f

        // Now set the points
        // TODO: OK, those points MUST come from our measures and
        // the actual bounds of the view!
        mA = Vector2D(mInitialEdgeOffset.toFloat(), 0f)
        mB = Vector2D(this.getWidth().toFloat(), this.getHeight().toFloat())
        mC = Vector2D(this.getWidth().toFloat(), 0f)
        mD = Vector2D(0f, 0f)
        mE = Vector2D(0f, 0f)
        mF = Vector2D(0f, 0f)
        mOldF = Vector2D(0f, 0f)

        // The movement origin point
        mOrigin = Vector2D(this.getWidth().toFloat(), 0f)
    }


    /**
     * See if the current curl mode is dynamic
     *
     * @return TRUE if the mode is CURLMODE_DYNAMIC, FALSE otherwise
     */
    fun IsCurlModeDynamic(): Boolean {
        return mCurlMode == CURLMODE_DYNAMIC
    }


    /**
     * @see measure
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val finalWidth: Int = measureWidth(widthMeasureSpec)
        val finalHeight: Int = measureHeight(heightMeasureSpec)
        setMeasuredDimension(finalWidth, finalHeight)
    }

    /**
     * Determines the width of this view
     *
     * @param measureSpec A measureSpec packed into an int
     * @return The width of the view, honoring constraints from measureSpec
     */
    private fun measureWidth(measureSpec: Int): Int {
        var result: Int
        val specMode = MeasureSpec.getMode(measureSpec)
        val specSize = MeasureSpec.getSize(measureSpec)

        if (specMode == MeasureSpec.EXACTLY) {
            // We were told how big to be
            result = specSize
        } else {
            // Measure the text
            result = specSize
        }

        return result
    }

    /**
     * Determines the height of this view
     *
     * @param measureSpec A measureSpec packed into an int
     * @return The height of the view, honoring constraints from measureSpec
     */
    private fun measureHeight(measureSpec: Int): Int {
        var result: Int
        val specMode = MeasureSpec.getMode(measureSpec)
        val specSize = MeasureSpec.getSize(measureSpec)

        if (specMode == MeasureSpec.EXACTLY) {
            // We were told how big to be
            result = specSize
        } else {
            // Measure the text (beware: ascent is a negative number)
            result = specSize
        }
        return result
    }


    var downArea = -1 //-1 为按下，0左、1中间、2右边

    override fun onTouchEvent(event: MotionEvent): Boolean {
        gestureDetector?.onTouchEvent(event)
        imageGestureListener?.onTouchEvent(event)

        if (!bBlockTouchInput) {
            // Get our finger position
            mFinger!!.x = event.x
            mFinger!!.y = event.y
            // Depending on the action do what we need to
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                }

                MotionEvent.ACTION_UP -> {

                }

                MotionEvent.ACTION_MOVE -> {

                }
            }
        }
        return true
    }

    /**
     * Make sure we never move too much, and make sure that if we
     * move too much  to add a displacement so that the movement will
     * be still in our radius.
     *
     * @param bMaintainMoveDir - Cap movement but do not change the
     * current movement direction
     * @return Corrected point
     */
    private fun CapMovement(point: Vector2D, bMaintainMoveDir: Boolean): Vector2D {
        // Make sure we never ever move too much
        var point = point
        if (point.distance(mOrigin!!) > mFlipRadius) {
            if (bMaintainMoveDir) {
                // Maintain the direction
                point = mOrigin!!.sum(point.sub(mOrigin!!).normalize().mult(mFlipRadius))
            } else {
                // Change direction
                if (point.x > (mOrigin!!.x + mFlipRadius)) point.x = (mOrigin!!.x + mFlipRadius)
                else if (point.x < (mOrigin!!.x - mFlipRadius)) point.x =
                    (mOrigin!!.x - mFlipRadius)
                point.y =
                    (sin(acos((abs(point.x - mOrigin!!.x) / mFlipRadius).toDouble())) * mFlipRadius).toFloat()
            }
        }
        return point
    }

    /**
     * Execute a step of the flip animation
     */
    fun FlipAnimationStep() {
        if (!bFlipping) return

        val width = getWidth()

        // No input when flipping
        bBlockTouchInput = true

        // Handle speed
        var curlSpeed = mCurlSpeed.toFloat()
        if (!bFlipRight) curlSpeed *= -1f

        // Move us
        mMovement!!.x += curlSpeed
        mMovement = CapMovement(mMovement!!, false)

        // Create values
        DoPageCurl()

        // Check for endings :D
        if (mA!!.x < 1 || mA!!.x > width - 1) {
            bFlipping = false
            if (bFlipRight) {
                nextView()
            } else {
                // go to next previous page
                previousView()
            }
            ResetClipEdge()

            // Create values
            DoPageCurl()

            // Enable touch input after the next draw event
            bEnableInputAfterDraw = true
        } else {
            mAnimationHandler!!.sleep(mUpdateRate.toLong())
        }

        // Force a new draw call
        this.invalidate()
    }

    /**
     * Do the page curl depending on the methods we are using
     */
    private fun DoPageCurl() {
        if (bFlipping) {
            if (IsCurlModeDynamic()) doDynamicCurl()
            else doSimpleCurl()
        } else {
            if (IsCurlModeDynamic()) doDynamicCurl()
            else doSimpleCurl()
        }
    }

    /**
     * Do a simple page curl effect
     */
    private fun doSimpleCurl() {
        val width = getWidth()
        val height = getHeight()

        // Calculate point A
        mA!!.x = width - mMovement!!.x
        mA!!.y = height.toFloat()

        // Calculate point D
        mD!!.x = 0f
        mD!!.y = 0f
        if (mA!!.x > width / 2) {
            mD!!.x = width.toFloat()
            mD!!.y = height - (width - mA!!.x) * height / mA!!.x
        } else {
            mD!!.x = 2 * mA!!.x
            mD!!.y = 0f
        }

        // Now calculate E and F taking into account that the line
        // AD is perpendicular to FB and EC. B and C are fixed points.
        val angle = atan(((height - mD!!.y) / (mD!!.x + mMovement!!.x - width)).toDouble())
        val _cos = cos(2 * angle)
        val _sin = sin(2 * angle)

        // And get F
        mF!!.x = (width - mMovement!!.x + _cos * mMovement!!.x).toFloat()
        mF!!.y = (height - _sin * mMovement!!.x).toFloat()

        // If the x position of A is above half of the page we are still not
        // folding the upper-right edge and so E and D are equal.
        if (mA!!.x > width / 2) {
            mE!!.x = mD!!.x
            mE!!.y = mD!!.y
        } else {
            // So get E
            mE!!.x = (mD!!.x + _cos * (width - mD!!.x)).toFloat()
            mE!!.y = -(_sin * (width - mD!!.x)).toFloat()
        }
    }

    /**
     * Calculate the dynamic effect, that one that follows the users finger
     */
    private fun doDynamicCurl() {
        val width = getWidth()
        val height = getHeight()

        // F will follow the finger, we add a small displacement
        // So that we can see the edge
        mF!!.x = width - mMovement!!.x + 0.1f
        mF!!.y = height - mMovement!!.y + 0.1f

        // Set min points
        if (mA!!.x == 0f) {
            mF!!.x = min(mF!!.x, mOldF!!.x)
            mF!!.y = max(mF!!.y, mOldF!!.y)
        }

        // Get diffs
        val deltaX = width - mF!!.x
        val deltaY = height - mF!!.y

        val BH = (sqrt((deltaX * deltaX + deltaY * deltaY).toDouble()) / 2).toFloat()
        val tangAlpha = (deltaY / deltaX).toDouble()
        val alpha = atan((deltaY / deltaX).toDouble())
        val _cos = cos(alpha)
        val _sin = sin(alpha)

        mA!!.x = (width - (BH / _cos)).toFloat()
        mA!!.y = height.toFloat()

        mD!!.y = (height - (BH / _sin)).toFloat()
        mD!!.x = width.toFloat()

        mA!!.x = max(0f, mA!!.x)
        if (mA!!.x == 0f) {
            mOldF!!.x = mF!!.x
            mOldF!!.y = mF!!.y
        }

        // Get W
        mE!!.x = mD!!.x
        mE!!.y = mD!!.y

        // Correct
        if (mD!!.y < 0) {
            mD!!.x = width + (tangAlpha * mD!!.y).toFloat()
            mE!!.y = 0f
            mE!!.x = width + (tan(2 * alpha) * mD!!.y).toFloat()
        }
    }

    /**
     * Swap between the fore and back-ground.
     */
    @Deprecated("")
    private fun SwapViews() {
        val temp = mForeground!!
        mForeground = mBackground
        mBackground = temp
    }

    private fun centerX(event: MotionEvent): Float {
        return (event.getX() + event.getX(1)) / 2
    }

    private fun centerY(event: MotionEvent): Float {
        return (event.getY() + event.getY(1)) / 2
    }

    private fun discance(event: MotionEvent): Float {
        val x1 = event.getX()
        val y1 = event.getY()

        val x2 = event.getX(1)
        val y2 = event.getY(1)

        return sqrt((x1 - x2).toDouble().pow(2.0) + (y1 - y2).toDouble().pow(2.0)).toFloat()
    }

    /**
     * Swap to next view
     */
    private fun nextView() {
        var foreIndex = mIndex + 1
        if (foreIndex >= pageCount) {
            return
        }
        var backIndex = foreIndex + 1
        if (backIndex >= pageCount) {
            return
        }
        mIndex = foreIndex
        loadPage(foreIndex, {
            mForeground = mBackground
            mBackground = it
            autoFit()
            invalidate()
        })
    }

    /**
     * Swap to previous view
     */
    private fun previousView() {
        val backIndex = mIndex
        var foreIndex = backIndex - 1
        if (foreIndex < 0) {
//            foreIndex = pageCount - 1
            return
        }
        mIndex = foreIndex
        loadPage(foreIndex, {
            mBackground = mForeground
            mForeground = it
            autoFit()
            invalidate()
        })
    }

    private fun loadPage(index: Int, onLoaded: (BitmapDrawable) -> Unit) {
        // 确保视图有有效尺寸
        if (width <= 0 || height <= 0) {
            postDelayed({ loadPage(index, onLoaded) }, 200) // 延迟到布局完成后执行
            return
        }
        val path = base?.getPageUrl( index)?.toString()
        LOG.d("loadPage", "path: $path")
        val target = object : CustomTarget<Bitmap?>() {
            override fun onResourceReady(
                resource: Bitmap,
                transition: Transition<in Bitmap?>?
            ) {
                if (resource != null) {
                    drawableWidth = resource.width
                    drawableHeight = resource.height
                    onLoaded(createBitmapDrawable(resource))
                }
            }

            override fun onLoadCleared(placeholder: Drawable?) {
            }

            override fun onLoadFailed(errorDrawable: Drawable?) {
                super.onLoadFailed(errorDrawable)
            }
        }
        currentTarget = target
        IMG.with(this.context)
            .asBitmap()
            .load(path)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .into<CustomTarget<Bitmap?>?>(target)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        // 尺寸变化时重新加载
        if (w > 0 && h > 0 && (w != oldw || h != oldh)) {
            reloadPages()
        }
    }

    private fun reloadPages() {
        mForeground = null
        mBackground = null
        loadPage(mIndex) {
            mForeground = it;
            invalidate()
        }
        if (mIndex + 1 < pageCount) {
            loadPage(mIndex + 1) {
                mBackground = it
                invalidate()
            }
        }
    }

    //---------------------------------------------------------------
    // Drawing methods
    //---------------------------------------------------------------
    override fun onDraw(canvas: Canvas) {
        // Always refresh offsets
//        mCurrentLeft = left
//        mCurrentTop = top

        // We need to initialize all size data when we first draw the view
        if (!bViewDrawn) {
            bViewDrawn = true;
            onFirstDrawEvent(canvas);
        }

        // Draw our elements
        drawForeground(canvas, null)
        drawBackground(canvas, null)
        drawCurlEdge(canvas)

        // Check if we can re-enable input
        if (bEnableInputAfterDraw) {
            bBlockTouchInput = false
            bEnableInputAfterDraw = false
        }
//         Restore canvas
//        canvas.restore()
    }

    /**
     * Called on the first draw event of the view
     *
     * @param canvas
     */
    protected fun onFirstDrawEvent(canvas: Canvas?) {
        mFlipRadius = getWidth().toFloat()
        ResetClipEdge()
        DoPageCurl()
    }

    /**
     * Draw the foreground
     *
     * @param canvas
     * @param rect
     * @param paint
     */
    private fun drawForeground(canvas: Canvas, paint: Paint?) {

        if (AppState.get().isOLED && !AppState.get().isDayNotInvert /* && MagicHelper.getBgColor() == Color.BLACK */) {
            canvas.drawColor(Color.BLACK)
        } else {
            canvas.drawColor(MagicHelper.ligtherColor(MagicHelper.getBgColor()))
        }
        val saveCount = canvas.saveCount
        canvas.save()
        canvas.concat(imageMatrix())
        if (mForeground == null) return
        mForeground!!.draw(canvas)

        if (PageImageState.get().isShowCuttingLine && AppSP.get().isCut == false) {
            val offset = drawableWidth * AppState.get().cutP / 100
            canvas.drawLine(
                offset.toFloat(),
                0f,
                offset.toFloat(),
                drawableHeight.toFloat(),
                paintWrods
            )
        }
//
        val selectedWords = PageImageState.get().getSelectedWords(mIndex)
        if (selectedWords != null) {
            for (tw in selectedWords) {
                drawWord(canvas, tw)
            }
        }
//
        if (AppState.get().isOLED && !AppState.get().isDayNotInvert /* && !TempHolder.get().isTextFormat */) {
            canvas.drawRect(
                -dp1.toFloat(),
                0f,
                (drawableWidth + dp1).toFloat(),
                drawableHeight.toFloat(),
                this.rect
            )
        }

        if (!AppSP.get().isCut && !AppSP.get().isCrop) {
            paintWrods.setColor(if (AppState.get().isDayNotInvert) Color.BLUE else Color.YELLOW)
            paintWrods.setAlpha(60)

            if (!BookCSS.get().isTextFormat()) {
                if (AppSP.get().isDouble) {
                    for (pl in getPageLinks(1)!!.first) {
                        drawLink(canvas, pl!!)
                    }

                    for (pl in getPageLinks(2)!!.first) {
                        drawLink(canvas, pl!!)
                    }
                } else {
                    for (pl in getPageLinks(0)!!.first) {
                        drawLink(canvas, pl!!)
                    }
                }
            }
        }
        canvas.restoreToCount(saveCount)
//        drawPageNum(canvas, mIndex)
    }

    /**
     * Create a Path used as a mask to draw the background page
     *
     * @return
     */
    private fun createBackgroundPath(): Path {
        val path = Path()
        path.moveTo(mA!!.x, mA!!.y)
        path.lineTo(mB!!.x, mB!!.y)
        path.lineTo(mC!!.x, mC!!.y)
        path.lineTo(mD!!.x, mD!!.y)
        path.lineTo(mA!!.x, mA!!.y)
        return path
    }

    /**
     * Draw the background image.
     *
     * @param canvas
     * @param rect
     * @param paint
     */
    private fun drawBackground(canvas: Canvas, paint: Paint?) {
        val mask = createBackgroundPath()


//            if (AppState.get().isOLED && !AppState.get().isDayNotInvert /* && MagicHelper.getBgColor() == Color.BLACK */) {
//                canvas.drawColor(Color.BLACK);
//            } else {
//                canvas.drawColor(MagicHelper.ligtherColor(MagicHelper.getBgColor()));
//            }
        val saveCount = canvas.getSaveCount()
        // Save current canvas so we do not mess it up
        canvas.save()
        canvas.clipPath(mask)
        canvas.concat(imageMatrix())
        if (mBackground == null) return
        mBackground!!.draw(canvas)
        canvas.restoreToCount(saveCount)

    }

    /**
     * Creates a path used to draw the curl edge in.
     *
     * @return
     */
    private fun createCurlEdgePath(): Path {
        val path = Path()
        path.moveTo(mA!!.x, mA!!.y)
        path.lineTo(mD!!.x, mD!!.y)
        path.lineTo(mE!!.x, mE!!.y)
        path.lineTo(mF!!.x, mF!!.y)
        path.lineTo(mA!!.x, mA!!.y)
        return path
    }

    /**
     * Draw the curl page edge
     *
     * @param canvas
     */
    private fun drawCurlEdge(canvas: Canvas) {
        val path = createCurlEdgePath()
        canvas.drawPath(path, mCurlEdgePaint!!)
    }

    /**
     * Draw page num (let this be a bit more custom)
     *
     * @param canvas
     * @param pageNum
     */
    private fun drawPageNum(canvas: Canvas, pageNum: Int) {
        mTextPaint!!.setColor(Color.WHITE)
        val pageNumText = "- " + pageNum + " -"
        drawCentered(
            canvas,
            pageNumText,
            canvas.getHeight() - mTextPaint!!.getTextSize() - 5,
            mTextPaint!!,
            mTextPaintShadow!!
        )
    }

    fun drawWord(c: Canvas, t: TextWord) {
        val o = transform(t, t.number)
        c.drawRect(o, paintWrods)
    }

    fun drawLink(c: Canvas, pl: PageLink) {
        val o = transform(pl.sourceRect, pl.number)
        c.drawLine(o.left, o.bottom, o.right, o.bottom, paintWrods)
        // c.drawText("" + pl.targetPage, o.right, o.bottom, paintWrods);
    }

    fun centerHorizontally() {
        val f = FloatArray(9)
        imageMatrix()!!.getValues(f)
        val tx = f[Matrix.MTRANS_X]
        val sx = f[Matrix.MSCALE_X]

        imageMatrix()!!.postTranslate(getWidth() / 2 - tx - drawableWidth * sx / 2, 0f)
        LOG.d("centerHorizontally", width, tx, drawableWidth * sx)
    }

    @Subscribe
    fun onCenterHorizontally(event: MessageCenterHorizontally) {
        LOG.d("onCenterHorizontally recive")
        if (mIndex == event.getPage()) {
            LOG.d("onAutoFit recive", event.getPage(), mIndex)
            centerHorizontally()
            invalidate()
        }
    }

    fun imageMatrix(): Matrix? {
        return PageImageState.get().matrix
    }


    @Subscribe
    fun onInvalidate(event: InvalidateMessage?) {
        invalidate()
    }

    @Subscribe
    fun onSelectTextByAnchors(event: MessagePageXY) {
        if (mIndex == event.page && MessagePageXY.TYPE_SELECT_TEXT == event.getType()) {
            selectText(event.getX1(), event.getY1(), event.getX(), event.getY())
            Log.d("选择文本", "测试选择文本")
        }
    }

    ;

    @Subscribe
    fun onAutoFit(event: MessageAutoFit) {
        LOG.d("onAutoFit recive")
        if (mIndex == event.page) {
            LOG.d("onAutoFit recive", event.getPage(), mIndex)
            autoFit()
            invalidate()
            isFirstZoomInOut = true
        }
    }

    fun invalidateAndMsg() {
        EventBus.getDefault().post(InvalidateMessage())
    }

    fun autoFit() {
        if (!PageImageState.get().isAutoFit) {
            return
        }

        val w = width
        val h = height
        val scaleH: Float = h.toFloat() / drawableHeight
        val scaleW: Float = w.toFloat() / drawableWidth

        imageMatrix()!!.reset()
        if (scaleH < scaleW) {
            LOG.d("image pre scale scaleH", scaleH)
            imageMatrix()!!.preScale(scaleH, scaleH)
            imageMatrix()!!.postTranslate(abs(getWidth() - drawableWidth * scaleH) / 2, 0f)
        } else {
            LOG.d("image pre scale scaleW", scaleW)
            imageMatrix()!!.preScale(scaleW, scaleW)
            imageMatrix()!!.postTranslate(0f, abs(getHeight() - drawableHeight * scaleW) / 2)
        }
    }

    fun transform(origin: RectF?, number: Int): RectF {
        val r = RectF(origin)
        r.left = r.left * drawableWidth
        r.right = r.right * drawableWidth

        r.top = r.top * drawableHeight
        r.bottom = r.bottom * drawableHeight

        if (number == 1) {
            r.left = r.left / 2
            r.right = r.right / 2
        } else if (number == 2) {
            r.left = drawableWidth / 2 + r.left / 2
            r.right = drawableWidth / 2 + r.right / 2
        }

        return r
    }

    fun getPageText(number: Int): Array<Array<TextWord>> {
        return getPageText(mIndex, number)
    }

    fun getPageText(pageNumber: Int, number: Int): Array<Array<TextWord>> {
        try {
            if (AppSP.get().isDouble && number != 0) {
                var page = pageNumber * 2
                if (AppSP.get().isDoubleCoverAlone) {
                    page--
                }

                var t: Array<Array<TextWord>> = arrayOf()
                if (number == 1) {
                    t = PageImageState.get().pagesText.get(page)
                } else if (number == 2) {
                    t = PageImageState.get().pagesText.get(page + 1)
                }

                for (i in t!!.indices) {
                    for (j in t[i]!!.indices) {
                        // TextWord textWord = t[i][j];
                        // textWord.number = number;
                        t[i]!![j]!!.number = number
                    }
                }

                return t
            } else {
                return PageImageState.get().pagesText.get(pageNumber)
            }
        } catch (e: java.lang.Exception) {
            LOG.e(e)
            return arrayOf()
        }
    }

    @Synchronized
    fun getPageLinks(number: Int): Pair<MutableList<PageLink?>, MutableList<Annotation?>>? {
        if (AppSP.get().isCut || AppSP.get().isCrop) {
            return Pair<MutableList<PageLink?>, MutableList<Annotation?>>(
                mutableListOf<PageLink?>(),
                mutableListOf<Annotation?>()
            )
        }
        return getPageLinksInner(number)
    }

    @Synchronized
    fun getPageLinksInner(number: Int): Pair<MutableList<PageLink?>, MutableList<Annotation?>>? {
        try {
            var t: MutableList<PageLink?>? = null
            var a: MutableList<Annotation?>? = null

            if (AppSP.get().isDouble && number != 0) {
                var page: Int = mIndex * 2
                if (AppSP.get().isDoubleCoverAlone) {
                    page--
                }
                if (number == 1) {
                    t = PageImageState.get().pagesLinks.get(page)
                    a = PageImageState.get().pagesAnnotation.get(page)
                } else if (number == 2) {
                    t = PageImageState.get().pagesLinks.get(page + 1)
                    a = PageImageState.get().pagesAnnotation.get(page + 1)
                }

                if (t != null) {
                    for (l in t) {
                        l?.number = number
                    }
                }
            } else {
                t = PageImageState.get().pagesLinks.get(mIndex)
                a = PageImageState.get().pagesAnnotation.get(mIndex)
            }

            if (t == null) {
                t = mutableListOf()
            }
            if (a == null) {
                a = mutableListOf<Annotation?>()
            }
            return Pair<MutableList<PageLink?>, MutableList<Annotation?>>(t, a)
        } catch (e: java.lang.Exception) {
            LOG.e(e)
            return null
        }
    }

    fun selectText(x1: Float, y1: Float, xInit: Float, yInit: Float): String? {
        var xInit = xInit
        var yInit = yInit
        if (!AppSP.get().isDouble && getPageText(0) == null) {
            LOG.d("get pag No page text", mIndex)
            return null
        }

        val single = abs(x1 - xInit) < PageImaveView.MIN && abs(y1 - yInit) < PageImaveView.MIN

        val tr = RectF()
        imageMatrix()!!.mapRect(tr)

        var x = x1 - tr.left
        var y = y1 - tr.top

        xInit = xInit - tr.left
        yInit = yInit - tr.top

        val f = FloatArray(9)
        imageMatrix()!!.getValues(f)

        val scaleX = f[Matrix.MSCALE_X]

        x = x / scaleX
        y = y / scaleX

        xInit = xInit / scaleX
        yInit = yInit / scaleX

        val tapRect = RectF(xInit, yInit, x, y)
        if (yInit > y) {
            tapRect.sort()
        }

        PageImageState.get().cleanSelectedWords()

        val build = StringBuilder()

        var isHyphenWorld = false
        var prevWord: TextWord? = null

        var firstNumber = 0
        if (AppSP.get().isDouble) {
            firstNumber = if (xInit < drawableWidth / 2) 1 else 2
        }
        TempHolder.get().textFromPage = firstNumber

        LOG.d("firstNumber", firstNumber)
        val pageText: Array<Array<TextWord>> = getPageText(firstNumber)
        if (pageText == null) {
            return null
        }
        for (line in pageText) {
            if (line == null) {
                continue
            }
            val current: Array<TextWord> = line
            for (textWord in current!!) {
                if (textWord == null) {
                    continue
                }
                if (!BookCSS.get().isTextFormat && (textWord.left < 0 || textWord.top < 0)) {
                    continue
                }

                val wordRect: RectF = transform(textWord, firstNumber)
                if (single) {
                    val intersects = RectF.intersects(wordRect, tapRect)
                    if (intersects || isHyphenWorld) {
                        LOG.d("ADD TEXT", textWord)

                        if (prevWord != null && prevWord.w.endsWith("-") && !isHyphenWorld) {
                            build.append(prevWord.w.replace("-", ""))
                            PageImageState.get().addWord(mIndex, prevWord)
                        }

                        if (!isHyphenWorld) {
                            PageImageState.get().addWord(mIndex, textWord)
                        }

                        if (isHyphenWorld && TxtUtils.isNotEmpty(textWord.getWord())) {
                            PageImageState.get().addWord(mIndex, textWord)
                            isHyphenWorld = false
                        }
                        if (textWord.getWord().endsWith("-")) {
                            isHyphenWorld = true
                        }
                        build.append(textWord.getWord() + " ")
                    }
                } else {
                    if (y > yInit) {
                        if (wordRect.top < tapRect.top && wordRect.bottom > tapRect.top && wordRect.right > tapRect.left) {
                            PageImageState.get().addWord(mIndex, textWord)
                            build.append(textWord.getWord() + TxtUtils.space())
                        } else if (wordRect.top < tapRect.bottom && wordRect.bottom > tapRect.bottom && wordRect.left < tapRect.right) {
                            PageImageState.get().addWord(mIndex, textWord)
                            build.append(textWord.getWord() + TxtUtils.space())
                        } else if (wordRect.top > tapRect.top && wordRect.bottom < tapRect.bottom) {
                            PageImageState.get().addWord(mIndex, textWord)
                            build.append(textWord.getWord() + TxtUtils.space())
                        }
                    } else if (RectF.intersects(wordRect, tapRect)) {
                        PageImageState.get().addWord(mIndex, textWord)
                        if (AppState.get().selectingByLetters) {
                            build.append(textWord.w)
                        } else {
                            build.append(textWord.w.trim { it <= ' ' } + " ")
                        }
                    }
                }

                if (TxtUtils.isNotEmpty(textWord.w)) {
                    prevWord = textWord
                }
            }
            var k: String? = null
            if (AppState.get().selectingByLetters && current.size >= 2 && ((current[current.size - 1]!!.word.also {
                    k = it
                }) != " ") && (k != "-")) {
                build.append(" ")
            }
        }


        var txt: String? = build.toString()
        if (txt!!.endsWith("- ")) {
            try {
                if (firstNumber == 0) {
                    val texts: Array<Array<TextWord>> = getPageText(mIndex + 1, 0)
                    if (texts!![0]!!.size > 1) {
                        txt += texts[0]!![1]!!.w
                    } else {
                        txt += texts[0]!![0]!!.w
                    }
                } else if (firstNumber == 1) {
                    txt += getPageText(mIndex, 2)[0][1].w
                } else {
                    txt += getPageText(mIndex + 1, 1)[0][1].w
                }
            } catch (e: Exception) {
                LOG.e(e)
            }
        }
        txt = TxtUtils.filterString(txt)

        AppState.get().selectedText = txt
        invalidate()
        return txt
    }

    fun getPageLinkClicked(x1: Float, y1: Float): Pair<PageLink?, Annotation?> {
        var x1 = x1
        val tr = RectF()
        imageMatrix()!!.mapRect(tr)

        var x = x1 - tr.left
        var y = y1 - tr.top

        val f = FloatArray(9)
        imageMatrix()!!.getValues(f)

        val scaleX = f[Matrix.MSCALE_X]

        x = x / scaleX
        y = y / scaleX

        val tapRect = RectF(x, y, x, y)

        x1 = x1 - tr.left
        x1 = x1 / scaleX

        var firstNumber = 0
        if (AppSP.get().isDouble) {
            firstNumber = if (x1 < drawableWidth / 2) 1 else 2
        }

        var p: PageLink? = null
        var a: Annotation? = null
        val pageLinks = getPageLinks(firstNumber)!!.first
        val annotations = getPageLinks(firstNumber)!!.second

        LOG.d(
            "getPageLinkClicked",
            x1,
            "w",
            drawableWidth,
            firstNumber,
            "links",
            pageLinks.size
        )

        for (link in pageLinks) {
            if (link == null) {
                continue
            }
            val wordRect = transform(link.sourceRect, firstNumber)
            val intersects = RectF.intersects(wordRect, tapRect)
            if (intersects) {
                p = link
                break
            }
        }
        for (link in annotations) {
            if (link == null) {
                continue
            }
            val wordRect = transform(link, firstNumber)
            val intersects = RectF.intersects(wordRect, tapRect)
            if (intersects) {
                a = link
                LOG.d("Link Clicked", a.text)
                break
            }
        }

        return Pair<PageLink?, Annotation?>(p, a)
    }


    fun zoomInOut(e: MotionEvent) {
        if (isFirstZoomInOut) {
            imageMatrix()?.preTranslate(
                getWidth() / 2 - e.getX(),
                getHeight() / 2 - e.getY()
            )
            imageMatrix()?.postScale(
                2.5f,
                2.5f,
                (getWidth() / 2).toFloat(),
                (getHeight() / 2).toFloat()
            )
            isFirstZoomInOut = false
            prevLock = AppSP.get().isLocked
            AppSP.get().isLocked = false
            invalidateAndMsg()
            PageImageState.get().isAutoFit = false
        } else {
            AppSP.get().isLocked = prevLock
            if (BookCSS.get().isTextFormat()) {
                AppSP.get().isLocked = true
            }
            isLognPress = true
            PageImageState.get().isAutoFit = true
            autoFit()
            invalidateAndMsg()
            isFirstZoomInOut = true
        }
    }

    inner class ImageSimpleGestureListener : SimpleTouchOnGestureListener() {

        override fun onDoubleTap(e: MotionEvent): Boolean {
            clickUtils!!.init()
            isIgronerClick = true
            if (clickUtils!!.isClickCenter(e.getX(), e.getY())) {
                isLognPress = true

                if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_NOTHING) {
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_ZOOM_IN_OUT) {
                    zoomInOut(e)
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CLOSE_BOOK) {
                    EventBus.getDefault()
                        .post(MessageEvent(MessageEvent.MESSAGE_CLOSE_BOOK, e.getX(), e.getY()))
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_AUTOSCROLL) {
                    EventBus.getDefault().post(MessageEvent(MessageEvent.MESSAGE_AUTO_SCROLL))
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CLOSE_BOOK_AND_APP) {
                    EventBus.getDefault()
                        .post(
                            MessageEvent(
                                MessageEvent.MESSAGE_CLOSE_BOOK_APP,
                                e.getX(),
                                e.getY()
                            )
                        )
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CLOSE_HIDE_APP) {
                    Apps.showDesctop(getContext())
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_START_STOP_TTS) {
                    EventBus.getDefault()
                        .post(MessageEvent(MessageEvent.MESSAGE_PLAY_PAUSE, e.getX(), e.getY()))
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_SHARE_PAGE) {
                    EventBus.getDefault()
                        .post(MessageEvent(MessageEvent.MESSAGE_SHARE_PAGE, e.getX(), e.getY()))
                } else if (AppState.get().doubleClickAction1 == AppState.DOUBLE_CLICK_CENTER_HORIZONTAL) {
                    PageImageState.get().isAutoFit = false
                    onCenterHorizontally(MessageCenterHorizontally(mIndex))
                } else {
                    PageImageState.get().isAutoFit = true
                    autoFit()
                    invalidateAndMsg()
                }

                EventBus.getDefault()
                    .post(MessageEvent(MessageEvent.MESSAGE_DOUBLE_TAP, e.getX(), e.getY()))
                return true
            }

            return true
        }


        override fun onFling(
            e1: MotionEvent?,
            e2: MotionEvent,
            velocityX: Float,
            velocityY: Float
        ): Boolean {
            if (bBlockTouchInput) return false
            if (e1 == null) return false
            if (e1.x < BrightnessHelper.BRIGHTNESS_WIDTH) {
                return false
            }
            if (AppState.get().selectedText != null) {
                return false
            }
            if (AppSP.get().isLocked) {
                return false
            }
            if (isReadyForMove) {
                // 移动界面
                isIgronerClick = true
                scroller!!.fling(
                    e2.x.toInt(),
                    e2.getY().toInt(),
                    velocityX.toInt() / 3,
                    velocityY.toInt() / 3,
                    Int.Companion.MIN_VALUE,
                    Int.Companion.MAX_VALUE,
                    Int.Companion.MIN_VALUE,
                    Int.Companion.MAX_VALUE
                )
                handler?.post(scrolling)
            }
            return true
        }

        override fun onLongPress(e: MotionEvent) {
            if (bBlockTouchInput) return
            isIgronerClick = true
            Log.d("测试：", "长按文本,isSelectTexByTouch:${AppState.get().isSelectTexByTouch}")
//            if (AppState.get().isSelectTexByTouch) {
//                //EventBus.getDefault().post(new MessageEvent(MessageEvent.MESSAGE_PERFORM_CLICK, e.getX(), e.getY()));
//                isLognPress = false
//                isIgronerClick = true
//                AppState.get().selectedText = null
//                EventBus.getDefault().post(MessagePageXY(MessagePageXY.TYPE_HIDE))
//                if (ClickUtils().isClickCenter(e.getX(), e.getY())) {
//                    EventBus.getDefault()
//                        .post(
//                            MessageEvent(
//                                MessageEvent.MESSAGE_PERFORM_CLICK,
//                                e.getX(),
//                                e.getY()
//                            )
//                        )
//                }
//                LOG.d("PageImaveView MESSAGE_PERFORM_CLICK", 3)
//                return
//            }

            if (!AppState.get().isAllowTextSelection) {
                Toast.makeText(
                    AdamApp.context,
                    R.string.text_highlight_mode_is_disable,
                    Toast.LENGTH_LONG
                ).show()
                return
            }
            //震动
            Vibro.vibrate()
            if (AppSP.get().isCut || AppSP.get().isCrop) {
                Toast.makeText(
                    AdamApp.context,
                    R.string.the_page_is_clipped_the_text_selection_does_not_work,
                    Toast.LENGTH_LONG
                ).show()
                return
            }
            isLognPress = true
            xInit = e.x
            yInit = e.y
            val selectText: String? = selectText(xInit, yInit, e.getX(), e.getY())
            Log.d("测试：", "长按文本: ${selectText}")
            if (TxtUtils.isEmpty(selectText)) {
                AppState.get().selectedText = null
                EventBus.getDefault().post(MessagePageXY(MessagePageXY.TYPE_HIDE))
            }
        }

        fun getDownArea(x: Float, y: Float): Int {
            Log.d(
                "测试",
                "点击坐标x:${x},y:${y}."+
                "左边区域:${leftRect}"+
                "中间区域:${centerRect}"+
                "右边区域:${rightRect}"
            )
            return when {
                leftRect!!.contains(x, y) -> 0
                centerRect!!.contains(x, y) -> 1
                rightRect!!.contains(x, y) -> 2
                else -> -1
            }
        }

        fun curlAnimDownEvent(downArea: Int) {
            mOldMovement!!.x = mFinger!!.x
            mOldMovement!!.y = mFinger!!.y
            // If we moved over the half of the display flip to next
            if (downArea == 2) {
                mMovement!!.x = mInitialEdgeOffset.toFloat()
                mMovement!!.y = mInitialEdgeOffset.toFloat()
                // Set the right movement flag
                bFlipRight = true
                nextView()
            } else if (downArea == 0) {
                // Set the left movement flag
                bFlipRight = false
                // go to next previous page
                previousView()
                // Set new movement
                mMovement!!.x = (if (IsCurlModeDynamic()) width shl 1 else width).toFloat()
                mMovement!!.y = mInitialEdgeOffset.toFloat()
            }
        }

        fun curlAnimMoveEvent() {
            bUserMoves = true
            // Get movement
            mMovement!!.x -= mFinger!!.x - mOldMovement!!.x
            mMovement!!.y -= mFinger!!.y - mOldMovement!!.y
            mMovement = CapMovement(mMovement!!, true)

            // Make sure the y value get's locked at a nice level
            if (mMovement!!.y <= 1) mMovement!!.y = 1f

            // Get movement direction
            if (mFinger!!.x < mOldMovement!!.x) {
                bFlipRight = true
            } else {
                bFlipRight = false
            }

            // Save old movement values
            mOldMovement!!.x = mFinger!!.x
            mOldMovement!!.y = mFinger!!.y
            // Force a new draw call
            DoPageCurl()
            <EMAIL>()
        }

        fun curAnimUpEvent() {
            bUserMoves = false
            bFlipping = true
            FlipAnimationStep()
        }

        override fun onTouchEvent(event: MotionEvent): Boolean {
            val action = event.action and MotionEvent.ACTION_MASK
            mFinger!!.x = event.x
            mFinger!!.y = event.y
            if (bBlockTouchInput) return true
            if (action == MotionEvent.ACTION_DOWN) {
                AppState.get().selectedText = null
                LOG.d("TEST", "action ACTION_DOWN")
                scroller!!.forceFinished(true)
                downX = event.x
                downY = event.y
                brightnessHelper!!.onActoinDown(downX, downY)
                isReadyForMove = false
                isLognPress = false
                isMoveNextPrev = 0
                downArea = getDownArea(event.x, event.y)
                Log.d("测试","点击区域：${downArea}")
                curlAnimDownEvent(downArea)
                EventBus.getDefault().post(MessagePageXY(MessagePageXY.TYPE_HIDE))
            } else if (action == MotionEvent.ACTION_MOVE) {
                if (event.pointerCount == 1) {
                    LOG.d("TEST", "action ACTION_MOVE 1")
                    val dx = event.x - downX
                    val dy = event.y - downY
                    if (isLognPress) {
                        val selectText: String? =
                            selectText(event.x, event.y, xInit, yInit)
                        if (selectText != null && selectText.contains(" ")) {
                            EventBus.getDefault().post(
                                MessagePageXY(
                                    MessagePageXY.TYPE_SHOW,
                                    -1,
                                    xInit,
                                    yInit,
                                    event.getX(),
                                    event.getY()
                                )
                            )
                        }
                    } else {
                        if (AppSP.get().isLocked) {
                            isReadyForMove = false
                            isIgronerClick = false
                            if (AppState.get().isEnableVerticalSwipe && abs(dy) > abs(dx) && abs(dy) > Dips.DP_10) {
                                isMoveNextPrev = if (AppState.get().isSwipeGestureReverse) {
                                    if (dy > 0) -1 else 1
                                } else {
                                    if (dy > 0) 1 else -1
                                }
                            }
                        } else {
                            if (AppState.get().rotateViewPager == 0) {
                                if (abs(dy) > abs(dx) && (abs(dy) + abs(dx) > Dips.DP_10)) {
                                    isReadyForMove = true
                                    isIgronerClick = true
                                }
                            } else {
                                if (abs(dx) > abs(dy) && (abs(dx) + abs(dy) > Dips.DP_10)) {
                                    isReadyForMove = true
                                    isIgronerClick = true
                                }
                            }
                        }
                        val isBrightness = brightnessHelper!!.onActionMove(event)
                        if (isBrightness) {
                            isIgronerClick = true
                            isMoveNextPrev = 0
                        }
                        // 点击左右的区域才可以执行滑动动画
                        if (downArea == 0 || downArea == 2) {
                            if (isMoveNextPrev != 0) {
                                curlAnimMoveEvent()
                            }
                        } else if (isFirstZoomInOut.not()) {
                            if (!isBrightness && isReadyForMove && !AppSP.get().isLocked) {
                                imageMatrix()?.postTranslate(dx, dy)

                                PageImageState.get().isAutoFit = false
                                invalidateAndMsg()

                                downX = event.x
                                downY = event.y
                            }
                        }
                    }
                }
                if (event.pointerCount == 2) {
                    isIgronerClick = true

                    LOG.d("TEST", "action ACTION_MOVE 2")
                    if (cx == 0f) {
                        cx = centerX(event)
                        cy = centerY(event)
                    }
                    val nDistance: Float = discance(event)

                    if (distance == 0f) {
                        distance = nDistance
                    }

                    val scale = nDistance / distance
                    distance = nDistance
                    val centerX: Float = centerX(event)
                    val centerY: Float = centerY(event)

                    val values: FloatArray? = FloatArray(9)
                    imageMatrix()?.getValues(values)

                    if (AppState.get().isZoomInOutWithLock || !AppSP.get().isLocked) {
                        LOG.d("postScale", scale, values!![Matrix.MSCALE_X])
                        if (values[Matrix.MSCALE_X] > 0.3f || scale > 1) {
                            imageMatrix()?.postScale(scale, scale, centerX, centerY)
                            EventBus.getDefault().post(MessagePageXY(MessagePageXY.TYPE_HIDE))
                        }
                    }
                    val dx = centerX - cx
                    val dy = centerY - cy
                    if (AppState.get().isZoomInOutWithLock || !AppSP.get().isLocked) {
                        imageMatrix()?.postTranslate(dx, dy)
                        EventBus.getDefault().post(MessagePageXY(MessagePageXY.TYPE_HIDE))
                    }
                    cx = centerX(event)
                    cy = centerY(event)

                    PageImageState.get().isAutoFit = false
                    invalidateAndMsg()
                }
            } else if (action == MotionEvent.ACTION_POINTER_UP) {
                LOG.d("TEST", "action ACTION_POINTER_UP")
                // isDoubleTouch = true;
                val actionIndex = event.actionIndex
                LOG.d("TEST", "actionIndex $actionIndex")
                if (actionIndex == 1) {
                    downX = event.getX()
                    downY = event.getY()
                } else {
                    downX = event.getX(1)
                    downY = event.getY(1)
                }
                cx = 0f
                distance = 0f
            } else if (action == MotionEvent.ACTION_UP) {
                brightnessHelper!!.onActionUp()

                LOG.d("TEST", "action ACTION_UP", "long: " + isLognPress)
                distance = 0f
                isReadyForMove = false
                cx = 0f
                cy = 0f

                if (isLognPress) {
                    val selectText: String? = selectText(event.getX(), event.getY(), xInit, yInit)
                    if (selectText != null && selectText.contains(" ")) {
                        EventBus.getDefault().post(
                            MessagePageXY(
                                MessagePageXY.TYPE_SHOW,
                                -1,
                                xInit,
                                yInit,
                                event.getX(),
                                event.getY()
                            )
                        )
                    }
                }
//                else if (BookCSS.get().isTextFormat()) {
//                    if (!TempHolder.isSeaching) {
//                        selectText(event.getX(), event.getY(), event.getX(), event.getY())
//                        if (!TxtUtils.isFooterNote(AppState.get().selectedText)) {
//                            PageImageState.get().cleanSelectedWords()
//                            AppState.get().selectedText = null
//                            invalidate()
//
//                            EventBus.getDefault().post(MessagePageXY(MessagePageXY.TYPE_HIDE))
//                        }
//                    }
//                }

                if (!isIgronerClick) {
                    var target = 0
                    val pair: Pair<PageLink?, Annotation?> =
                        getPageLinkClicked(event.getX(), event.getY())
                    val pageLink = pair.first
                    if (pageLink != null) {
                        target = pageLink.targetPage
                        if (AppSP.get().isDouble && target != -1) {
                            target = pageLink.targetPage / 2
                        }
                        TempHolder.get().linkPage = target
                        LOG.d("Go to targetPage", target)
                    }
                    if (pair.second != null) {
                        Dialogs.showTextDialog(getContext(), pair.second!!.text)
                    }

                    if (isMoveNextPrev != 0) {
                        LOG.d("isMoveNextPrev", isMoveNextPrev)
                        bUserMoves = false
                        bFlipping = true
                        FlipAnimationStep()
                        EventBus.getDefault().post(
                            MessageEvent(
                                MessageEvent.MESSAGE_GOTO_PAGE_SWIPE,
                                isMoveNextPrev
                            )
                        )
                    } else if (TxtUtils.isNotEmpty(AppState.get().selectedText)) {
                        EventBus.getDefault().post(MessageEvent(MessageEvent.MESSAGE_SELECTED_TEXT))
                    } else if (pageLink != null) {
                        EventBus.getDefault().post(
                            MessageEvent(
                                MessageEvent.MESSAGE_GOTO_PAGE_BY_LINK,
                                target,
                                pageLink.url
                            )
                        )
                    } else {
                        Log.d("测试", "downArea:${downArea}")
                        if (downArea == 0) {
                            previousView()
                        } else if (downArea == 2) {
                            nextView()
                        }
                        LOG.d("PageImaveView MESSAGE_PERFORM_CLICK", 1)
                        EventBus.getDefault().post(
                            MessageEvent(
                                MessageEvent.MESSAGE_PERFORM_CLICK,
                                event.getX(),
                                event.getY()
                            )
                        )
                    }
                } else {

                    if (TxtUtils.isNotEmpty(AppState.get().selectedText)) {
                        EventBus.getDefault().post(MessageEvent(MessageEvent.MESSAGE_SELECTED_TEXT))
                    } else if (AppState.get().isSelectTexByTouch && !ClickUtils().isClickCenter(
                            event.getX(),
                            event.getY()
                        )
                    ) {
                        LOG.d("PageImaveView MESSAGE_PERFORM_CLICK", 2)
                        EventBus.getDefault().post(
                            MessageEvent(
                                MessageEvent.MESSAGE_PERFORM_CLICK,
                                event.getX(),
                                event.getY()
                            )
                        )
                    }
                }

                isIgronerClick = false
            } else if (action == MotionEvent.ACTION_CANCEL) {
                LOG.d("TEST", "action ACTION_CANCEL")
            }

            return true
        }
    }


    companion object {
        /**
         * Our Log tag
         */
        private const val TAG = "PageCurlView"

        /**
         * Simple curl mode. Curl target will move only in one axis.
         */
        const val CURLMODE_SIMPLE: Int = 0

        /**
         * Dynamic curl mode. Curl target will move on both X and Y axis.
         */
        const val CURLMODE_DYNAMIC: Int = 1

        //---------------------------------------------------------------
        // Debug draw methods
        //---------------------------------------------------------------
        /**
         * Draw a text with a nice shadow
         */
        fun drawTextShadowed(
            canvas: Canvas,
            text: String,
            x: Float,
            y: Float,
            textPain: Paint,
            shadowPaint: Paint
        ) {
            canvas.drawText(text, x - 1, y, shadowPaint)
            canvas.drawText(text, x, y + 1, shadowPaint)
            canvas.drawText(text, x + 1, y, shadowPaint)
            canvas.drawText(text, x, y - 1, shadowPaint)
            canvas.drawText(text, x, y, textPain)
        }

        /**
         * Draw a text with a nice shadow centered in the X axis
         *
         * @param canvas
         * @param text
         * @param y
         * @param textPain
         * @param shadowPaint
         */
        fun drawCentered(
            canvas: Canvas,
            text: String,
            y: Float,
            textPain: Paint,
            shadowPaint: Paint
        ) {
            val posx = (canvas.getWidth() - textPain.measureText(text)) / 2
            drawTextShadowed(canvas, text, posx, y, textPain, shadowPaint)
        }
    }
}